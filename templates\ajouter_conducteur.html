{% extends "base.html" %}

{% block title %}Ajouter un Conducteur - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-plus"></i> Ajouter un Conducteur</h1>
            <a href="{{ url_for('conducteurs') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Informations du conducteur
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Nom -->
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">
                                <i class="fas fa-user"></i> Nom *
                            </label>
                            <input type="text" class="form-control" id="nom" 
                                   name="nom" required
                                   placeholder="Dupont">
                            <div class="invalid-feedback">
                                Veuillez saisir le nom du conducteur
                            </div>
                        </div>

                        <!-- Prénom -->
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">
                                <i class="fas fa-user"></i> Prénom *
                            </label>
                            <input type="text" class="form-control" id="prenom" 
                                   name="prenom" required
                                   placeholder="Jean">
                            <div class="invalid-feedback">
                                Veuillez saisir le prénom du conducteur
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Numéro de permis -->
                        <div class="col-md-6 mb-3">
                            <label for="numero_permis" class="form-label">
                                <i class="fas fa-id-card"></i> Numéro de permis *
                            </label>
                            <input type="text" class="form-control" id="numero_permis" 
                                   name="numero_permis" required
                                   placeholder="123456789"
                                   pattern="[0-9]{9,12}"
                                   title="Numéro de permis (9 à 12 chiffres)">
                            <div class="invalid-feedback">
                                Veuillez saisir un numéro de permis valide (9 à 12 chiffres)
                            </div>
                        </div>

                        <!-- Date du permis -->
                        <div class="col-md-6 mb-3">
                            <label for="date_permis" class="form-label">
                                <i class="fas fa-calendar"></i> Date d'obtention du permis
                            </label>
                            <input type="date" class="form-control" id="date_permis" 
                                   name="date_permis"
                                   max="{{ date_today }}">
                            <small class="form-text text-muted">
                                Date d'obtention du permis de conduire
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Téléphone -->
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">
                                <i class="fas fa-phone"></i> Téléphone
                            </label>
                            <input type="tel" class="form-control" id="telephone" 
                                   name="telephone"
                                   placeholder="01 23 45 67 89"
                                   pattern="[0-9\s\-\+\(\)\.]{10,}"
                                   title="Numéro de téléphone valide">
                            <small class="form-text text-muted">
                                Format: 01 23 45 67 89 ou +33 1 23 45 67 89
                            </small>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <input type="email" class="form-control" id="email" 
                                   name="email"
                                   placeholder="<EMAIL>">
                            <div class="invalid-feedback">
                                Veuillez saisir une adresse email valide
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Statut -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-info-circle"></i> Statut
                            </label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="actif" selected>Actif</option>
                                <option value="inactif">Inactif</option>
                            </select>
                            <small class="form-text text-muted">
                                Statut du conducteur dans l'entreprise
                            </small>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Information:</strong> 
                                Les champs marqués d'un astérisque (*) sont obligatoires. 
                                Les informations de contact permettront de joindre le conducteur en cas de besoin.
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('conducteurs') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Enregistrer le conducteur
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Formatage automatique du téléphone
    const telInput = document.getElementById('telephone');
    telInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
        }
        this.value = value;
    });
    
    // Validation de l'âge minimum pour le permis
    const datePermisInput = document.getElementById('date_permis');
    datePermisInput.addEventListener('change', function() {
        const datePermis = new Date(this.value);
        const today = new Date();
        const age = today.getFullYear() - datePermis.getFullYear();
        
        if (age > 80) {
            alert('Attention: Cette date de permis semble très ancienne. Veuillez vérifier.');
        }
    });
    
    // Mise à jour automatique de la date max pour aujourd'hui
    const today = new Date().toISOString().split('T')[0];
    datePermisInput.setAttribute('max', today);
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Nouvelle Affectation - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> Nouvelle Affectation</h1>
            <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Affecter un véhicule à un conducteur
                </h5>
            </div>
            <div class="card-body">
                {% if not vehicules %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Aucun véhicule disponible</strong><br>
                    Tous les véhicules sont déjà affectés ou indisponibles.
                    <a href="{{ url_for('vehicules') }}" class="btn btn-sm btn-outline-primary ms-2">
                        Voir les véhicules
                    </a>
                </div>
                {% elif not conducteurs %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Aucun conducteur actif</strong><br>
                    Aucun conducteur actif n'est disponible pour une affectation.
                    <a href="{{ url_for('conducteurs') }}" class="btn btn-sm btn-outline-primary ms-2">
                        Voir les conducteurs
                    </a>
                </div>
                {% else %}
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Véhicule -->
                        <div class="col-md-6 mb-3">
                            <label for="vehicule_id" class="form-label">
                                <i class="fas fa-car"></i> Véhicule *
                            </label>
                            <select class="form-select" id="vehicule_id" name="vehicule_id" required>
                                <option value="">Sélectionner un véhicule</option>
                                {% for vehicule in vehicules %}
                                <option value="{{ vehicule.id }}" 
                                        data-marque="{{ vehicule.marque }}"
                                        data-modele="{{ vehicule.modele }}"
                                        data-annee="{{ vehicule.annee }}"
                                        data-carburant="{{ vehicule.carburant }}">
                                    {{ vehicule.immatriculation }} - {{ vehicule.marque }} {{ vehicule.modele }} ({{ vehicule.annee }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un véhicule
                            </div>
                            <div id="vehicule-info" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="vehicule-details"></span>
                                </small>
                            </div>
                        </div>

                        <!-- Conducteur -->
                        <div class="col-md-6 mb-3">
                            <label for="conducteur_id" class="form-label">
                                <i class="fas fa-user"></i> Conducteur *
                            </label>
                            <select class="form-select" id="conducteur_id" name="conducteur_id" required>
                                <option value="">Sélectionner un conducteur</option>
                                {% for conducteur in conducteurs %}
                                <option value="{{ conducteur.id }}"
                                        data-permis="{{ conducteur.numero_permis }}"
                                        data-telephone="{{ conducteur.telephone or '' }}"
                                        data-email="{{ conducteur.email or '' }}">
                                    {{ conducteur.prenom }} {{ conducteur.nom }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un conducteur
                            </div>
                            <div id="conducteur-info" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="conducteur-details"></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date de début -->
                        <div class="col-md-6 mb-3">
                            <label for="date_debut" class="form-label">
                                <i class="fas fa-calendar-plus"></i> Date de début *
                            </label>
                            <input type="date" class="form-control" id="date_debut" 
                                   name="date_debut" required
                                   value="{{ date_today }}"
                                   min="{{ date_today }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date de début
                            </div>
                        </div>

                        <!-- Date de fin (optionnelle) -->
                        <div class="col-md-6 mb-3">
                            <label for="date_fin" class="form-label">
                                <i class="fas fa-calendar-minus"></i> Date de fin (optionnelle)
                            </label>
                            <input type="date" class="form-control" id="date_fin" 
                                   name="date_fin"
                                   min="{{ date_today }}">
                            <small class="form-text text-muted">
                                Laisser vide pour une affectation à durée indéterminée
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Commentaire -->
                        <div class="col-12 mb-3">
                            <label for="commentaire" class="form-label">
                                <i class="fas fa-comment"></i> Commentaire
                            </label>
                            <textarea class="form-control" id="commentaire" name="commentaire" 
                                      rows="3" placeholder="Motif de l'affectation, conditions particulières..."></textarea>
                        </div>
                    </div>

                    <!-- Résumé de l'affectation -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info" id="resume-affectation" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Résumé de l'affectation</h6>
                                <div id="resume-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('affectations') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Créer l'affectation
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-car"></i> Véhicules disponibles</h6>
                <h4 class="text-primary">{{ vehicules|length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-users"></i> Conducteurs actifs</h6>
                <h4 class="text-success">{{ conducteurs|length }}</h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Affichage des détails du véhicule
    const vehiculeSelect = document.getElementById('vehicule_id');
    const vehiculeInfo = document.getElementById('vehicule-info');
    const vehiculeDetails = document.getElementById('vehicule-details');
    
    vehiculeSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const marque = option.dataset.marque;
            const modele = option.dataset.modele;
            const annee = option.dataset.annee;
            const carburant = option.dataset.carburant;
            
            vehiculeDetails.innerHTML = `${marque} ${modele} (${annee}) - ${carburant}`;
            vehiculeInfo.style.display = 'block';
        } else {
            vehiculeInfo.style.display = 'none';
        }
        updateResume();
    });
    
    // Affichage des détails du conducteur
    const conducteurSelect = document.getElementById('conducteur_id');
    const conducteurInfo = document.getElementById('conducteur-info');
    const conducteurDetails = document.getElementById('conducteur-details');
    
    conducteurSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const permis = option.dataset.permis;
            const telephone = option.dataset.telephone;
            const email = option.dataset.email;
            
            let details = `Permis: ${permis}`;
            if (telephone) details += ` - Tél: ${telephone}`;
            if (email) details += ` - Email: ${email}`;
            
            conducteurDetails.innerHTML = details;
            conducteurInfo.style.display = 'block';
        } else {
            conducteurInfo.style.display = 'none';
        }
        updateResume();
    });
    
    // Validation des dates
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    
    dateDebut.addEventListener('change', function() {
        dateFin.min = this.value;
        updateResume();
    });
    
    dateFin.addEventListener('change', updateResume);
    
    // Mise à jour du résumé
    function updateResume() {
        const vehiculeOption = vehiculeSelect.options[vehiculeSelect.selectedIndex];
        const conducteurOption = conducteurSelect.options[conducteurSelect.selectedIndex];
        const debut = dateDebut.value;
        const fin = dateFin.value;
        
        if (vehiculeOption.value && conducteurOption.value && debut) {
            const resumeDiv = document.getElementById('resume-affectation');
            const resumeContent = document.getElementById('resume-content');
            
            let html = `
                <strong>Véhicule:</strong> ${vehiculeOption.text}<br>
                <strong>Conducteur:</strong> ${conducteurOption.text}<br>
                <strong>Période:</strong> Du ${debut}${fin ? ' au ' + fin : ' (durée indéterminée)'}
            `;
            
            resumeContent.innerHTML = html;
            resumeDiv.style.display = 'block';
        } else {
            document.getElementById('resume-affectation').style.display = 'none';
        }
    }
    
    // Définir la date d'aujourd'hui par défaut
    const today = new Date().toISOString().split('T')[0];
    dateDebut.value = today;
    dateDebut.min = today;
    dateFin.min = today;
});
</script>
{% endblock %}

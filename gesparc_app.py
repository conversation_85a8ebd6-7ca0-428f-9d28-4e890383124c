#!/usr/bin/env python3
"""
Application Flask pour la gestion de parc automobile - GesParc Auto
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
import sys
from datetime import datetime
from export_utils import export_to_csv, export_to_excel_xlsx, export_to_excel_xls, get_export_filename
from config import get_config
from immatriculation_maroc import ImmatriculationMaroc
from matplotlib_analytics import GesparcAnalytics

# Créer l'application Flask
gesparc_app = Flask(__name__)

# Charger la configuration appropriée
config_class = get_config()
gesparc_app.config.from_object(config_class)

# Configuration pour Apache avec préfixe /gesparc
GESPARC_PREFIX = '/gesparc'

# Détecter si on est dans un contexte Apache ou si on veut forcer le préfixe
USE_PREFIX = (
    os.environ.get('SCRIPT_NAME') == GESPARC_PREFIX or
    os.environ.get('GESPARC_USE_PREFIX', '').lower() == 'true' or
    '--prefix' in sys.argv
)

if USE_PREFIX:
    gesparc_app.config['APPLICATION_ROOT'] = GESPARC_PREFIX
    print(f"🌐 Configuration Flask avec préfixe: {GESPARC_PREFIX}")

# Initialisation spécifique pour Apache si nécessaire
if hasattr(config_class, 'init_app'):
    config_class.init_app(gesparc_app)

# Middleware pour gérer le préfixe /gesparc
class PrefixMiddleware(object):
    def __init__(self, app, prefix=''):
        self.app = app
        self.prefix = prefix

    def __call__(self, environ, start_response):
        if self.prefix and environ['PATH_INFO'].startswith(self.prefix):
            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]
            environ['SCRIPT_NAME'] = self.prefix
            return self.app(environ, start_response)
        elif self.prefix:
            # Si on a un préfixe mais que l'URL ne commence pas par le préfixe
            start_response('404', [('Content-Type', 'text/plain')])
            return [b"Cette URL n'appartient pas a l'application."]
        else:
            return self.app(environ, start_response)

# Appliquer le middleware si nécessaire
if USE_PREFIX:
    gesparc_app.wsgi_app = PrefixMiddleware(gesparc_app.wsgi_app, prefix=GESPARC_PREFIX)

# Fonction helper pour les URLs avec préfixe
def url_for_prefix(endpoint, **values):
    """Génère une URL en tenant compte du préfixe /gesparc"""
    if USE_PREFIX:
        return GESPARC_PREFIX + url_for(endpoint, **values)
    else:
        return url_for(endpoint, **values)

# Rendre la fonction disponible dans les templates
@gesparc_app.context_processor
def inject_url_helpers():
    return dict(
        url_for_prefix=url_for_prefix,
        USE_PREFIX=USE_PREFIX,
        GESPARC_PREFIX=GESPARC_PREFIX if USE_PREFIX else ''
    )

    def __call__(self, environ, start_response):
        if environ['PATH_INFO'].startswith(self.prefix):
            environ['PATH_INFO'] = environ['PATH_INFO'][len(self.prefix):]
            environ['SCRIPT_NAME'] = self.prefix
            return self.app(environ, start_response)
        else:
            start_response('404', [('Content-Type', 'text/plain')])
            return ["This url does not belong to the app.".encode()]

# Appliquer le middleware si on est sous Apache
if os.environ.get('SCRIPT_NAME') or gesparc_app.config.get('APPLICATION_ROOT', '/') != '/':
    prefix = gesparc_app.config.get('APPLICATION_ROOT', '/gesparc')
    gesparc_app.wsgi_app = PrefixMiddleware(gesparc_app.wsgi_app, prefix=prefix)

# Rendre les fonctions utilitaires disponibles dans les templates
@gesparc_app.template_filter('format_prix')
def format_prix_filter(prix):
    """Filtre Jinja2 pour formater les prix"""
    return format_prix(prix)

# Configuration de la base de données
DATABASE = gesparc_app.config.get('DATABASE', 'parc_automobile.db')

# Configuration de la devise
DEVISE = 'MAD'  # Dirham Marocain

def format_prix(prix):
    """Formate un prix avec la devise MAD"""
    if prix is None:
        return '-'
    return f"{prix:,.2f}".replace(',', ' ') + f" {DEVISE}"

def get_db_connection():
    """Établit une connexion à la base de données SQLite"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@gesparc_app.route('/')
def index():
    """Page d'accueil avec tableau de bord"""
    
    # Récupérer les statistiques
    stats = {
        'total_vehicules': 0,
        'vehicules_disponibles': 0,
        'total_conducteurs': 0,
        'maintenances_prevues': 0
    }
    
    maintenances_prochaines = []
    
    try:
        conn = get_db_connection()
        
        # Statistiques véhicules
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
        
        # Statistiques conducteurs
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        
        # Maintenances prévues
        stats['maintenances_prevues'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
        
        # Maintenances à venir (prochains 30 jours)
        maintenances_prochaines = conn.execute('''
            SELECT m.*, v.immatriculation 
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.statut = 'planifiee' 
            AND date(m.date_maintenance) BETWEEN date('now') AND date('now', '+30 days')
            ORDER BY m.date_maintenance
            LIMIT 5
        ''').fetchall()
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de la récupération des statistiques: {e}")
    
    return render_template('index.html', stats=stats, maintenances_prochaines=maintenances_prochaines)

# Routes pour la gestion des véhicules
@gesparc_app.route('/vehicules')
def vehicules():
    """Liste des véhicules"""
    try:
        conn = get_db_connection()
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()

        # Statistiques
        stats = {}
        stats['total'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]

        statuts = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM vehicules
            GROUP BY statut
        ''').fetchall()

        for statut in statuts:
            stats[statut['statut']] = statut['count']

        conn.close()

        return render_template('vehicules.html', vehicules=vehicules, stats=stats)

    except Exception as e:
        flash(f'Erreur lors de la récupération des véhicules: {e}', 'error')
        return render_template('vehicules.html', vehicules=[], stats={})

@gesparc_app.route('/vehicules/ajouter', methods=['GET', 'POST'])
def ajouter_vehicule():
    """Ajouter un nouveau véhicule"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            immatriculation_brute = request.form['immatriculation'].strip()
            marque = request.form['marque']
            modele = request.form['modele']
            annee = int(request.form['annee'])
            couleur = request.form.get('couleur', '')
            kilometrage = int(request.form.get('kilometrage', 0))
            carburant = request.form['carburant']
            statut = request.form.get('statut', 'disponible')
            date_acquisition = request.form.get('date_acquisition', '')
            prix_acquisition = request.form.get('prix_acquisition', '')

            # Validation de l'immatriculation marocaine
            validation_immat = ImmatriculationMaroc.valider(immatriculation_brute)
            if not validation_immat['valide']:
                flash(f'Immatriculation invalide: {validation_immat["message"]}', 'error')
                return render_template('ajouter_vehicule.html')

            # Utiliser l'immatriculation formatée
            immatriculation = validation_immat['immatriculation_formatee']

            # Validation basique
            if not all([immatriculation, marque, modele, annee, carburant]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return render_template('ajouter_vehicule.html')

            # Vérifier si l'immatriculation existe déjà
            conn = get_db_connection()
            existing = conn.execute('SELECT id FROM vehicules WHERE immatriculation = ?', (immatriculation,)).fetchone()
            if existing:
                flash(f'Un véhicule avec l\'immatriculation {immatriculation} existe déjà', 'error')
                conn.close()
                return render_template('ajouter_vehicule.html')

            # Insérer le véhicule
            conn.execute('''
                INSERT INTO vehicules (immatriculation, marque, modele, annee, couleur,
                kilometrage, carburant, statut, date_acquisition, prix_acquisition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (immatriculation, marque, modele, annee, couleur, kilometrage,
                  carburant, statut, date_acquisition or None,
                  float(prix_acquisition) if prix_acquisition else None))

            conn.commit()
            conn.close()

            flash(f'Véhicule {immatriculation} ajouté avec succès!', 'success')
            return redirect(url_for('vehicules'))

        except Exception as e:
            flash(f'Erreur lors de l\'ajout du véhicule: {e}', 'error')

    return render_template('ajouter_vehicule.html')

@gesparc_app.route('/vehicules/<int:id>')
def voir_vehicule(id):
    """Voir les détails d'un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        # Récupérer les maintenances du véhicule
        maintenances = conn.execute('''
            SELECT * FROM maintenances
            WHERE vehicule_id = ?
            ORDER BY date_maintenance DESC
        ''', (id,)).fetchall()

        # Récupérer les affectations du véhicule
        affectations = conn.execute('''
            SELECT a.*, c.nom, c.prenom
            FROM affectations a
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.vehicule_id = ?
            ORDER BY a.date_debut DESC
        ''', (id,)).fetchall()

        conn.close()

        return render_template('voir_vehicule.html', vehicule=vehicule,
                             maintenances=maintenances, affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/vehicules/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_vehicule(id):
    """Modifier un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        if request.method == 'POST':
            # Récupérer les données du formulaire
            immatriculation_brute = request.form['immatriculation'].strip()
            marque = request.form['marque']
            modele = request.form['modele']
            annee = int(request.form['annee'])
            couleur = request.form.get('couleur', '')
            kilometrage = int(request.form.get('kilometrage', 0))

            # Validation de l'immatriculation marocaine
            validation_immat = ImmatriculationMaroc.valider(immatriculation_brute)
            if not validation_immat['valide']:
                flash(f'Immatriculation invalide: {validation_immat["message"]}', 'error')
                return redirect(url_for('modifier_vehicule', id=id))

            # Utiliser l'immatriculation formatée
            immatriculation = validation_immat['immatriculation_formatee']
            carburant = request.form['carburant']
            statut = request.form.get('statut', 'disponible')
            date_acquisition = request.form.get('date_acquisition', '')
            prix_acquisition = request.form.get('prix_acquisition', '')

            # Vérifier si l'immatriculation existe déjà (sauf pour ce véhicule)
            existing = conn.execute('SELECT id FROM vehicules WHERE immatriculation = ? AND id != ?',
                                  (immatriculation, id)).fetchone()
            if existing:
                flash(f'Un autre véhicule avec l\'immatriculation {immatriculation} existe déjà', 'error')
                conn.close()
                return render_template('modifier_vehicule.html', vehicule=vehicule)

            # Mettre à jour le véhicule
            conn.execute('''
                UPDATE vehicules SET immatriculation=?, marque=?, modele=?, annee=?,
                couleur=?, kilometrage=?, carburant=?, statut=?, date_acquisition=?, prix_acquisition=?
                WHERE id=?
            ''', (immatriculation, marque, modele, annee, couleur, kilometrage,
                  carburant, statut, date_acquisition or None,
                  float(prix_acquisition) if prix_acquisition else None, id))

            conn.commit()
            conn.close()

            flash(f'Véhicule {immatriculation} modifié avec succès!', 'success')
            return redirect(url_for('voir_vehicule', id=id))

        conn.close()
        return render_template('modifier_vehicule.html', vehicule=vehicule)

    except Exception as e:
        flash(f'Erreur lors de la modification du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/vehicules/<int:id>/supprimer')
def supprimer_vehicule(id):
    """Supprimer un véhicule"""
    try:
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (id,)).fetchone()

        if not vehicule:
            flash('Véhicule non trouvé', 'error')
            return redirect(url_for('vehicules'))

        # Vérifier s'il y a des affectations actives
        affectations_actives = conn.execute('''
            SELECT COUNT(*) FROM affectations
            WHERE vehicule_id = ? AND statut = 'active'
        ''', (id,)).fetchone()[0]

        if affectations_actives > 0:
            flash('Impossible de supprimer ce véhicule car il a des affectations actives', 'error')
            conn.close()
            return redirect(url_for('vehicules'))

        # Supprimer le véhicule
        conn.execute('DELETE FROM vehicules WHERE id = ?', (id,))
        conn.commit()
        conn.close()

        flash(f'Véhicule {vehicule["immatriculation"]} supprimé avec succès!', 'success')
        return redirect(url_for('vehicules'))

    except Exception as e:
        flash(f'Erreur lors de la suppression du véhicule: {e}', 'error')
        return redirect(url_for('vehicules'))

# Routes pour la gestion des conducteurs
@gesparc_app.route('/conducteurs')
def conducteurs():
    """Liste des conducteurs"""
    try:
        conn = get_db_connection()

        # Récupérer les conducteurs avec leurs véhicules affectés
        conducteurs = conn.execute('''
            SELECT c.*,
                   CASE WHEN a.vehicule_id IS NOT NULL
                        THEN v.immatriculation
                        ELSE NULL
                   END as vehicule_affecte
            FROM conducteurs c
            LEFT JOIN affectations a ON c.id = a.conducteur_id AND a.statut = 'active'
            LEFT JOIN vehicules v ON a.vehicule_id = v.id
            ORDER BY c.nom, c.prenom
        ''').fetchall()

        # Statistiques
        stats = {}
        stats['total'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]

        statuts = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM conducteurs
            GROUP BY statut
        ''').fetchall()

        for statut in statuts:
            stats[statut['statut']] = statut['count']

        # Nombre de conducteurs avec véhicule affecté
        affectations_actives = conn.execute('''
            SELECT COUNT(DISTINCT conducteur_id)
            FROM affectations
            WHERE statut = 'active'
        ''').fetchone()[0]

        conn.close()

        return render_template('conducteurs.html',
                             conducteurs=conducteurs,
                             stats=stats,
                             affectations_actives=affectations_actives)

    except Exception as e:
        flash(f'Erreur lors de la récupération des conducteurs: {e}', 'error')
        return render_template('conducteurs.html', conducteurs=[], stats={}, affectations_actives=0)

@gesparc_app.route('/conducteurs/ajouter', methods=['GET', 'POST'])
def ajouter_conducteur():
    """Ajouter un nouveau conducteur"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            nom = request.form['nom'].strip()
            prenom = request.form['prenom'].strip()
            numero_permis = request.form['numero_permis'].strip()
            date_permis = request.form.get('date_permis', '')
            telephone = request.form.get('telephone', '').strip()
            email = request.form.get('email', '').strip()
            statut = request.form.get('statut', 'actif')

            # Validation
            if not all([nom, prenom, numero_permis]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return render_template('ajouter_conducteur.html')

            # Vérifier si le numéro de permis existe déjà
            conn = get_db_connection()
            existing = conn.execute('SELECT id FROM conducteurs WHERE numero_permis = ?', (numero_permis,)).fetchone()
            if existing:
                flash(f'Un conducteur avec le numéro de permis {numero_permis} existe déjà', 'error')
                conn.close()
                return render_template('ajouter_conducteur.html')

            # Insérer le conducteur
            conn.execute('''
                INSERT INTO conducteurs (nom, prenom, numero_permis, date_permis,
                telephone, email, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (nom, prenom, numero_permis, date_permis or None,
                  telephone or None, email or None, statut))

            conn.commit()
            conn.close()

            flash(f'Conducteur {prenom} {nom} ajouté avec succès!', 'success')
            return redirect(url_for('conducteurs'))

        except Exception as e:
            flash(f'Erreur lors de l\'ajout du conducteur: {e}', 'error')

    return render_template('ajouter_conducteur.html')

@gesparc_app.route('/conducteurs/<int:id>')
def voir_conducteur(id):
    """Voir les détails d'un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        # Récupérer les affectations du conducteur
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            WHERE a.conducteur_id = ?
            ORDER BY a.date_debut DESC
        ''', (id,)).fetchall()

        conn.close()

        return render_template('voir_conducteur.html', conducteur=conducteur, affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/conducteurs/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_conducteur(id):
    """Modifier un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        if request.method == 'POST':
            # Récupérer les données du formulaire
            nom = request.form['nom'].strip()
            prenom = request.form['prenom'].strip()
            numero_permis = request.form['numero_permis'].strip()
            date_permis = request.form.get('date_permis', '')
            telephone = request.form.get('telephone', '').strip()
            email = request.form.get('email', '').strip()
            statut = request.form.get('statut', 'actif')

            # Vérifier si le numéro de permis existe déjà (sauf pour ce conducteur)
            existing = conn.execute('SELECT id FROM conducteurs WHERE numero_permis = ? AND id != ?',
                                  (numero_permis, id)).fetchone()
            if existing:
                flash(f'Un autre conducteur avec le numéro de permis {numero_permis} existe déjà', 'error')
                conn.close()
                return render_template('modifier_conducteur.html', conducteur=conducteur)

            # Mettre à jour le conducteur
            conn.execute('''
                UPDATE conducteurs SET nom=?, prenom=?, numero_permis=?, date_permis=?,
                telephone=?, email=?, statut=? WHERE id=?
            ''', (nom, prenom, numero_permis, date_permis or None,
                  telephone or None, email or None, statut, id))

            conn.commit()
            conn.close()

            flash(f'Conducteur {prenom} {nom} modifié avec succès!', 'success')
            return redirect(url_for('voir_conducteur', id=id))

        conn.close()
        return render_template('modifier_conducteur.html', conducteur=conducteur)

    except Exception as e:
        flash(f'Erreur lors de la modification du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/conducteurs/<int:id>/supprimer')
def supprimer_conducteur(id):
    """Supprimer un conducteur"""
    try:
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (id,)).fetchone()

        if not conducteur:
            flash('Conducteur non trouvé', 'error')
            return redirect(url_for('conducteurs'))

        # Vérifier s'il y a des affectations actives
        affectations_actives = conn.execute('''
            SELECT COUNT(*) FROM affectations
            WHERE conducteur_id = ? AND statut = 'active'
        ''', (id,)).fetchone()[0]

        if affectations_actives > 0:
            flash('Impossible de supprimer ce conducteur car il a des affectations actives', 'error')
            conn.close()
            return redirect(url_for('conducteurs'))

        # Supprimer le conducteur
        conn.execute('DELETE FROM conducteurs WHERE id = ?', (id,))
        conn.commit()
        conn.close()

        flash(f'Conducteur {conducteur["prenom"]} {conducteur["nom"]} supprimé avec succès!', 'success')
        return redirect(url_for('conducteurs'))

    except Exception as e:
        flash(f'Erreur lors de la suppression du conducteur: {e}', 'error')
        return redirect(url_for('conducteurs'))

# Routes pour les maintenances
@gesparc_app.route('/maintenances')
def maintenances():
    """Liste des maintenances"""
    try:
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        conn.close()

        return render_template('maintenances.html', maintenances=maintenances)

    except Exception as e:
        flash(f'Erreur lors de la récupération des maintenances: {e}', 'error')
        return render_template('maintenances.html', maintenances=[])

@gesparc_app.route('/maintenances/ajouter', methods=['GET', 'POST'])
def ajouter_maintenance():
    """Planifier une nouvelle maintenance"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            vehicule_id = int(request.form['vehicule_id'])
            type_maintenance = request.form['type_maintenance']
            description = request.form.get('description', '').strip()
            date_maintenance = request.form['date_maintenance']
            cout_estime = request.form.get('cout_estime', '')
            garage = request.form.get('garage', '').strip()
            priorite = request.form.get('priorite', 'normale')
            statut = request.form.get('statut', 'planifiee')

            # Validation
            if not all([vehicule_id, type_maintenance, date_maintenance]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return redirect(url_for('ajouter_maintenance'))

            conn = get_db_connection()

            # Vérifier que le véhicule existe
            vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
            if not vehicule:
                flash('Véhicule non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_maintenance'))

            # Insérer la maintenance
            conn.execute('''
                INSERT INTO maintenances (vehicule_id, type_maintenance, description,
                date_maintenance, cout, garage, priorite, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (vehicule_id, type_maintenance, description or None, date_maintenance,
                  float(cout_estime) if cout_estime else None, garage or None, priorite, statut))

            conn.commit()
            conn.close()

            flash(f'Maintenance {type_maintenance} planifiée avec succès pour {vehicule["immatriculation"]}!', 'success')
            return redirect(url_for('maintenances'))

        except Exception as e:
            flash(f'Erreur lors de la planification de la maintenance: {e}', 'error')
            return redirect(url_for('ajouter_maintenance'))

    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()

        # Récupérer tous les véhicules
        vehicules = conn.execute('''
            SELECT * FROM vehicules
            ORDER BY immatriculation
        ''').fetchall()

        conn.close()

        from datetime import date
        date_today = date.today().isoformat()

        return render_template('planifier_maintenance.html',
                             vehicules=vehicules,
                             date_today=date_today)

    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/demarrer', methods=['POST'])
def demarrer_maintenance(id):
    """Démarrer une maintenance (planifiée -> en cours)"""
    try:
        conn = get_db_connection()

        # Vérifier que la maintenance existe et est planifiée
        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()
        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        if maintenance['statut'] != 'planifiee':
            flash('Cette maintenance ne peut pas être démarrée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Mettre à jour le statut
        conn.execute('''
            UPDATE maintenances
            SET statut = 'en_cours'
            WHERE id = ?
        ''', (id,))

        conn.commit()
        conn.close()

        flash(f'Maintenance {maintenance["type_maintenance"]} démarrée avec succès!', 'success')

    except Exception as e:
        flash(f'Erreur lors du démarrage de la maintenance: {e}', 'error')

    return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/terminer', methods=['POST'])
def terminer_maintenance(id):
    """Terminer une maintenance (en cours -> terminée)"""
    try:
        conn = get_db_connection()

        # Vérifier que la maintenance existe et est en cours
        maintenance = conn.execute('SELECT * FROM maintenances WHERE id = ?', (id,)).fetchone()
        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        if maintenance['statut'] != 'en_cours':
            flash('Cette maintenance ne peut pas être terminée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Mettre à jour le statut et la date de réalisation
        from datetime import date
        conn.execute('''
            UPDATE maintenances
            SET statut = 'terminee', date_realisation = ?
            WHERE id = ?
        ''', (date.today().isoformat(), id))

        conn.commit()
        conn.close()

        flash(f'Maintenance {maintenance["type_maintenance"]} terminée avec succès!', 'success')

    except Exception as e:
        flash(f'Erreur lors de la finalisation de la maintenance: {e}', 'error')

    return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/voir')
def voir_maintenance(id):
    """Voir les détails d'une maintenance"""
    try:
        conn = get_db_connection()

        # Récupérer la maintenance avec les détails du véhicule
        maintenance = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele, v.annee, v.kilometrage
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.id = ?
        ''', (id,)).fetchone()

        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        conn.close()

        return render_template('voir_maintenance.html', maintenance=maintenance)

    except Exception as e:
        flash(f'Erreur lors du chargement des détails: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/maintenances/<int:id>/modifier', methods=['GET', 'POST'])
def modifier_maintenance(id):
    """Modifier une maintenance"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            type_maintenance = request.form['type_maintenance']
            description = request.form.get('description', '').strip()
            date_maintenance = request.form['date_maintenance']
            cout = request.form.get('cout', '')
            garage = request.form.get('garage', '').strip()
            priorite = request.form.get('priorite', 'normale')
            statut = request.form.get('statut', 'planifiee')

            conn = get_db_connection()

            # Mettre à jour la maintenance
            conn.execute('''
                UPDATE maintenances
                SET type_maintenance = ?, description = ?, date_maintenance = ?,
                    cout = ?, garage = ?, priorite = ?, statut = ?
                WHERE id = ?
            ''', (type_maintenance, description or None, date_maintenance,
                  float(cout) if cout else None, garage or None, priorite, statut, id))

            conn.commit()
            conn.close()

            flash(f'Maintenance {type_maintenance} modifiée avec succès!', 'success')
            return redirect(url_for('maintenances'))

        except Exception as e:
            flash(f'Erreur lors de la modification: {e}', 'error')

    # GET request - afficher le formulaire de modification
    try:
        conn = get_db_connection()

        # Récupérer la maintenance
        maintenance = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.id = ?
        ''', (id,)).fetchone()

        if not maintenance:
            flash('Maintenance non trouvée', 'error')
            conn.close()
            return redirect(url_for('maintenances'))

        # Récupérer tous les véhicules pour le formulaire
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()

        conn.close()

        return render_template('modifier_maintenance.html',
                             maintenance=maintenance,
                             vehicules=vehicules)

    except Exception as e:
        flash(f'Erreur lors du chargement: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/api/valider-immatriculation', methods=['POST'])
def valider_immatriculation_api():
    """API pour valider une immatriculation en temps réel"""
    try:
        data = request.get_json()
        immatriculation = data.get('immatriculation', '').strip()

        if not immatriculation:
            return jsonify({
                'valide': False,
                'message': 'Immatriculation vide'
            })

        # Valider avec le module marocain
        validation = ImmatriculationMaroc.valider(immatriculation)

        # Vérifier l'unicité en base de données
        if validation['valide']:
            conn = get_db_connection()
            existing = conn.execute(
                'SELECT id FROM vehicules WHERE immatriculation = ?',
                (validation['immatriculation_formatee'],)
            ).fetchone()
            conn.close()

            if existing:
                validation['valide'] = False
                validation['message'] = 'Cette immatriculation existe déjà'

        return jsonify(validation)

    except Exception as e:
        return jsonify({
            'valide': False,
            'message': f'Erreur de validation: {str(e)}'
        })

@gesparc_app.route('/guide-immatriculation')
def guide_immatriculation():
    """Guide des formats d'immatriculation marocaine"""
    return render_template('guide_immatriculation.html')

# Routes pour les affectations
@gesparc_app.route('/affectations')
def affectations():
    """Liste des affectations"""
    try:
        conn = get_db_connection()
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        conn.close()

        return render_template('affectations.html', affectations=affectations)

    except Exception as e:
        flash(f'Erreur lors de la récupération des affectations: {e}', 'error')
        return render_template('affectations.html', affectations=[])

@gesparc_app.route('/affectations/ajouter', methods=['GET', 'POST'])
def ajouter_affectation():
    """Ajouter une nouvelle affectation"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            vehicule_id = int(request.form['vehicule_id'])
            conducteur_id = int(request.form['conducteur_id'])
            date_debut = request.form['date_debut']
            date_fin = request.form.get('date_fin', '')
            commentaire = request.form.get('commentaire', '').strip()

            # Validation
            if not all([vehicule_id, conducteur_id, date_debut]):
                flash('Veuillez remplir tous les champs obligatoires', 'error')
                return redirect(url_for('ajouter_affectation'))

            conn = get_db_connection()

            # Vérifier que le véhicule existe et est disponible
            vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
            if not vehicule:
                flash('Véhicule non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Vérifier que le conducteur existe et est actif
            conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (conducteur_id,)).fetchone()
            if not conducteur:
                flash('Conducteur non trouvé', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            if conducteur['statut'] != 'actif':
                flash('Le conducteur doit être actif pour recevoir une affectation', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Vérifier qu'il n'y a pas d'affectation active pour ce véhicule
            affectation_active = conn.execute('''
                SELECT id FROM affectations
                WHERE vehicule_id = ? AND statut = 'active'
            ''', (vehicule_id,)).fetchone()

            if affectation_active:
                flash('Ce véhicule a déjà une affectation active', 'error')
                conn.close()
                return redirect(url_for('ajouter_affectation'))

            # Créer l'affectation
            conn.execute('''
                INSERT INTO affectations (vehicule_id, conducteur_id, date_debut,
                date_fin, statut, commentaire)
                VALUES (?, ?, ?, ?, 'active', ?)
            ''', (vehicule_id, conducteur_id, date_debut,
                  date_fin or None, commentaire or None))

            # Mettre à jour le statut du véhicule
            conn.execute('''
                UPDATE vehicules SET statut = 'affecte' WHERE id = ?
            ''', (vehicule_id,))

            conn.commit()
            conn.close()

            flash(f'Affectation créée avec succès pour {conducteur["prenom"]} {conducteur["nom"]}!', 'success')
            return redirect(url_for('affectations'))

        except Exception as e:
            flash(f'Erreur lors de la création de l\'affectation: {e}', 'error')

    # GET request - afficher le formulaire
    try:
        conn = get_db_connection()

        # Récupérer les véhicules disponibles
        vehicules = conn.execute('''
            SELECT * FROM vehicules
            WHERE statut = 'disponible'
            ORDER BY immatriculation
        ''').fetchall()

        # Récupérer les conducteurs actifs
        conducteurs = conn.execute('''
            SELECT * FROM conducteurs
            WHERE statut = 'actif'
            ORDER BY nom, prenom
        ''').fetchall()

        conn.close()

        from datetime import date
        date_today = date.today().isoformat()

        return render_template('ajouter_affectation.html',
                             vehicules=vehicules,
                             conducteurs=conducteurs,
                             date_today=date_today)

    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/affectations/<int:id>/terminer')
def terminer_affectation(id):
    """Terminer une affectation"""
    try:
        conn = get_db_connection()

        # Récupérer l'affectation
        affectation = conn.execute('''
            SELECT a.*, v.immatriculation, c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            WHERE a.id = ?
        ''', (id,)).fetchone()

        if not affectation:
            flash('Affectation non trouvée', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        if affectation['statut'] != 'active':
            flash('Cette affectation n\'est pas active', 'error')
            conn.close()
            return redirect(url_for('affectations'))

        # Terminer l'affectation
        from datetime import date
        today = date.today().isoformat()

        conn.execute('''
            UPDATE affectations
            SET statut = 'terminee', date_fin = ?
            WHERE id = ?
        ''', (today, id))

        # Remettre le véhicule comme disponible
        conn.execute('''
            UPDATE vehicules SET statut = 'disponible'
            WHERE id = ?
        ''', (affectation['vehicule_id'],))

        conn.commit()
        conn.close()

        flash(f'Affectation terminée pour {affectation["prenom"]} {affectation["nom"]} ({affectation["immatriculation"]})', 'success')
        return redirect(url_for('affectations'))

    except Exception as e:
        flash(f'Erreur lors de la fin de l\'affectation: {e}', 'error')
        return redirect(url_for('affectations'))

# Routes pour les rapports
@gesparc_app.route('/rapports')
def rapports():
    """Page des rapports et statistiques"""
    print("🔍 DEBUG: Début de la route rapports")
    try:
        print("🔍 DEBUG: Connexion à la base de données")
        conn = get_db_connection()

        # Statistiques générales
        print("🔍 DEBUG: Calcul des statistiques générales")
        stats = {}
        stats['total_vehicules'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        stats['total_conducteurs'] = conn.execute('SELECT COUNT(*) FROM conducteurs').fetchone()[0]
        stats['total_maintenances'] = conn.execute('SELECT COUNT(*) FROM maintenances').fetchone()[0]
        stats['total_affectations'] = conn.execute('SELECT COUNT(*) FROM affectations').fetchone()[0]
        print(f"🔍 DEBUG: Stats générales OK: {stats}")

        # Statistiques détaillées
        stats['vehicules_disponibles'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0]
        stats['vehicules_en_maintenance'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0]
        stats['vehicules_affectes'] = conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'affecte'").fetchone()[0]
        stats['maintenances_planifiees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0]
        stats['maintenances_en_cours'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'en_cours'").fetchone()[0]
        stats['maintenances_terminees'] = conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'terminee'").fetchone()[0]
        stats['affectations_actives'] = conn.execute("SELECT COUNT(*) FROM affectations WHERE statut = 'active'").fetchone()[0]
        stats['conducteurs_actifs'] = conn.execute("SELECT COUNT(*) FROM conducteurs WHERE statut = 'actif'").fetchone()[0]

        # Coûts de maintenance et analyses financières
        cout_total_result = conn.execute('SELECT SUM(cout) FROM maintenances WHERE cout IS NOT NULL').fetchone()
        stats['cout_total_maintenances'] = cout_total_result[0] if cout_total_result[0] else 0

        cout_mois_result = conn.execute('''
            SELECT SUM(cout) FROM maintenances
            WHERE cout IS NOT NULL
            AND date_maintenance >= date('now', 'start of month')
        ''').fetchone()
        stats['cout_mois_maintenances'] = cout_mois_result[0] if cout_mois_result[0] else 0

        # Coût moyen par véhicule et par maintenance
        stats['cout_moyen_vehicule'] = (stats['cout_total_maintenances'] / stats['total_vehicules']) if stats['total_vehicules'] > 0 else 0
        stats['cout_moyen_maintenance'] = (stats['cout_total_maintenances'] / stats['total_maintenances']) if stats['total_maintenances'] > 0 else 0

        # Analyses de performance
        stats['taux_disponibilite'] = (stats['vehicules_disponibles'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_utilisation'] = (stats['vehicules_affectes'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0
        stats['taux_maintenance'] = (stats['vehicules_en_maintenance'] / stats['total_vehicules'] * 100) if stats['total_vehicules'] > 0 else 0

        # Kilométrage moyen
        km_moyen_result = conn.execute('SELECT AVG(kilometrage) FROM vehicules WHERE kilometrage > 0').fetchone()
        stats['kilometrage_moyen'] = km_moyen_result[0] if km_moyen_result[0] else 0

        # Âge moyen du parc
        age_moyen_result = conn.execute('SELECT AVG(2025 - annee) FROM vehicules').fetchone()
        stats['age_moyen_parc'] = age_moyen_result[0] if age_moyen_result[0] else 0

        # Répartition par statut des véhicules
        vehicules_par_statut = conn.execute('''
            SELECT statut, COUNT(*) as count
            FROM vehicules
            GROUP BY statut
            ORDER BY count DESC
        ''').fetchall()

        # Répartition par carburant
        vehicules_par_carburant = conn.execute('''
            SELECT carburant, COUNT(*) as count
            FROM vehicules
            WHERE carburant IS NOT NULL AND carburant != ''
            GROUP BY carburant
            ORDER BY count DESC
        ''').fetchall()

        # Répartition par marque
        vehicules_par_marque = conn.execute('''
            SELECT marque, COUNT(*) as count
            FROM vehicules
            WHERE marque IS NOT NULL AND marque != ''
            GROUP BY marque
            ORDER BY count DESC
            LIMIT 10
        ''').fetchall()

        # Maintenances par mois (12 derniers mois)
        maintenances_par_mois = conn.execute('''
            SELECT strftime('%Y-%m', date_maintenance) as mois,
                   COUNT(*) as count,
                   SUM(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_total
            FROM maintenances
            WHERE date_maintenance >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', date_maintenance)
            ORDER BY mois
        ''').fetchall()

        # Maintenances par type
        maintenances_par_type = conn.execute('''
            SELECT type_maintenance, COUNT(*) as count,
                   AVG(CASE WHEN cout IS NOT NULL THEN cout ELSE 0 END) as cout_moyen
            FROM maintenances
            GROUP BY type_maintenance
            ORDER BY count DESC
        ''').fetchall()

        # Top 5 véhicules avec le plus de maintenances
        vehicules_maintenances = conn.execute('''
            SELECT v.immatriculation, v.marque, v.modele, COUNT(m.id) as nb_maintenances,
                   SUM(CASE WHEN m.cout IS NOT NULL THEN m.cout ELSE 0 END) as cout_total
            FROM vehicules v
            LEFT JOIN maintenances m ON v.id = m.vehicule_id
            GROUP BY v.id
            ORDER BY nb_maintenances DESC, cout_total DESC
            LIMIT 5
        ''').fetchall()

        # Conducteurs avec véhicules affectés
        conducteurs_actifs = conn.execute('''
            SELECT c.nom, c.prenom, v.immatriculation, v.marque, v.modele, a.date_debut
            FROM conducteurs c
            JOIN affectations a ON c.id = a.conducteur_id
            JOIN vehicules v ON a.vehicule_id = v.id
            WHERE a.statut = 'active'
            ORDER BY c.nom, c.prenom
        ''').fetchall()

        # Analyses prédictives et alertes (version simplifiée)
        try:
            alertes = []
            predictions = []

            # Alertes simples
            if stats['vehicules_en_maintenance'] > 0:
                alertes.append({
                    'type': 'maintenance_en_cours',
                    'niveau': 'info',
                    'message': f'{stats["vehicules_en_maintenance"]} véhicule(s) en maintenance',
                    'vehicule': 'Multiple'
                })

            if stats['taux_disponibilite'] < 70:
                alertes.append({
                    'type': 'disponibilite_faible',
                    'niveau': 'warning',
                    'message': f'Taux de disponibilité faible: {stats["taux_disponibilite"]:.1f}%',
                    'vehicule': 'Parc'
                })

            stats['alertes'] = alertes
            stats['predictions'] = predictions
            stats['nb_alertes'] = len(alertes)
            stats['nb_predictions'] = len(predictions)
        except Exception as e:
            print(f"Erreur alertes: {e}")
            stats['alertes'] = []
            stats['predictions'] = []
            stats['nb_alertes'] = 0
            stats['nb_predictions'] = 0

        conn.close()

        print("🔍 DEBUG: Rendu du template debug")
        return render_template('rapports_debug.html',
                             stats=stats,
                             vehicules_par_statut=vehicules_par_statut,
                             vehicules_par_carburant=vehicules_par_carburant,
                             vehicules_par_marque=vehicules_par_marque,
                             maintenances_par_mois=maintenances_par_mois,
                             maintenances_par_type=maintenances_par_type,
                             vehicules_maintenances=vehicules_maintenances,
                             conducteurs_actifs=conducteurs_actifs)

    except Exception as e:
        print(f"❌ DEBUG: Erreur dans la route rapports: {e}")
        import traceback
        traceback.print_exc()
        flash(f'Erreur lors de la génération des rapports: {e}', 'error')
        return render_template('rapports_debug.html',
                             stats={'erreur': str(e)}, vehicules_par_statut=[], vehicules_par_carburant=[],
                             vehicules_par_marque=[], maintenances_par_mois=[], maintenances_par_type=[],
                             vehicules_maintenances=[], conducteurs_actifs=[])

@gesparc_app.route('/api/dashboard-data')
def api_dashboard_data():
    """API pour récupérer les données du dashboard en temps réel"""
    try:
        conn = get_db_connection()

        # Données de base
        data = {
            'timestamp': datetime.now().isoformat(),
            'stats': {
                'total_vehicules': conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0],
                'vehicules_disponibles': conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'disponible'").fetchone()[0],
                'vehicules_en_maintenance': conn.execute("SELECT COUNT(*) FROM vehicules WHERE statut = 'en_maintenance'").fetchone()[0],
                'maintenances_planifiees': conn.execute("SELECT COUNT(*) FROM maintenances WHERE statut = 'planifiee'").fetchone()[0],
                'cout_mois': conn.execute('''
                    SELECT COALESCE(SUM(cout), 0) FROM maintenances
                    WHERE cout IS NOT NULL AND date_maintenance >= date('now', 'start of month')
                ''').fetchone()[0]
            },
            'alertes_recentes': []
        }

        # Alertes récentes (dernières 24h)
        alertes = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            WHERE m.date_creation >= date('now', '-1 day')
            AND m.statut = 'planifiee'
            ORDER BY m.date_creation DESC
            LIMIT 5
        ''').fetchall()

        for alerte in alertes:
            data['alertes_recentes'].append({
                'vehicule': alerte[0],
                'type': alerte[1],
                'date': alerte[2],
                'priorite': alerte[3]
            })

        conn.close()
        return jsonify(data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/api/dashboard-filters')
def api_dashboard_filters():
    """API pour appliquer des filtres au dashboard"""
    try:
        period = request.args.get('period', '30')
        vehicle_type = request.args.get('vehicle_type', 'all')
        status = request.args.get('status', 'all')

        conn = get_db_connection()

        # Construction de la requête avec filtres
        where_conditions = []
        params = []

        if vehicle_type != 'all':
            where_conditions.append('v.carburant = ?')
            params.append(vehicle_type)

        if status != 'all':
            where_conditions.append('v.statut = ?')
            params.append(status)

        where_clause = ' AND '.join(where_conditions)
        if where_clause:
            where_clause = 'WHERE ' + where_clause

        # Données filtrées
        query = f'''
            SELECT COUNT(*) FROM vehicules v {where_clause}
        '''

        total_filtered = conn.execute(query, params).fetchone()[0]

        conn.close()

        return jsonify({
            'total_filtered': total_filtered,
            'filters_applied': {
                'period': period,
                'vehicle_type': vehicle_type,
                'status': status
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/rapports/export/<format>')
def export_rapports(format):
    """Exporter les rapports en différents formats"""
    try:
        if format == 'csv':
            return export_rapports_csv()
        elif format == 'excel':
            return export_rapports_excel()
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('rapports'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('rapports'))

def export_rapports_csv():
    """Exporter les rapports en CSV"""
    import csv
    import io
    from flask import make_response

    try:
        conn = get_db_connection()

        output = io.StringIO()
        writer = csv.writer(output)

        # Export des véhicules
        output.write("=== VEHICULES ===\n")
        writer.writerow(['Immatriculation', 'Marque', 'Modèle', 'Année', 'Statut', 'Carburant', 'Kilométrage'])
        vehicules = conn.execute('SELECT immatriculation, marque, modele, annee, statut, carburant, kilometrage FROM vehicules ORDER BY immatriculation').fetchall()
        for row in vehicules:
            writer.writerow(row)

        output.write("\n=== MAINTENANCES ===\n")
        writer.writerow(['Véhicule', 'Type', 'Date', 'Coût (MAD)', 'Garage', 'Statut', 'Priorité'])
        maintenances = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance,
                   m.cout, m.garage, m.statut, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        for row in maintenances:
            writer.writerow(row)

        conn.close()

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=rapports_gesparc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        flash(f'Erreur export CSV: {e}', 'error')
        return redirect(url_for('rapports'))

def export_rapports_excel():
    """Exporter les rapports en Excel"""
    try:
        # Tentative d'import d'openpyxl
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            flash('Module openpyxl non installé. Export Excel non disponible.', 'error')
            return redirect(url_for('rapports'))

        import io
        from flask import make_response

        conn = get_db_connection()

        wb = openpyxl.Workbook()

        # Feuille Véhicules
        ws_vehicules = wb.active
        ws_vehicules.title = "Véhicules"
        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Statut', 'Carburant', 'Kilométrage']
        ws_vehicules.append(headers)

        # Style pour les en-têtes
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        for col in range(1, len(headers) + 1):
            cell = ws_vehicules.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill

        vehicules = conn.execute('SELECT immatriculation, marque, modele, annee, statut, carburant, kilometrage FROM vehicules ORDER BY immatriculation').fetchall()
        for row in vehicules:
            ws_vehicules.append(row)

        # Feuille Maintenances
        ws_maintenances = wb.create_sheet("Maintenances")
        headers = ['Véhicule', 'Type', 'Date', 'Coût (MAD)', 'Garage', 'Statut', 'Priorité']
        ws_maintenances.append(headers)

        for col in range(1, len(headers) + 1):
            cell = ws_maintenances.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill

        maintenances = conn.execute('''
            SELECT v.immatriculation, m.type_maintenance, m.date_maintenance,
                   m.cout, m.garage, m.statut, m.priorite
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        for row in maintenances:
            ws_maintenances.append(row)

        conn.close()

        # Sauvegarder en mémoire
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = make_response(output.read())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=rapports_gesparc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return response

    except Exception as e:
        flash(f'Erreur export Excel: {e}', 'error')
        return redirect(url_for('rapports'))

# Routes d'export
@gesparc_app.route('/export/vehicules/<format>')
def export_vehicules(format):
    """Exporter la liste des véhicules"""
    try:
        conn = get_db_connection()
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()
        conn.close()

        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Couleur',
                  'Kilométrage', 'Carburant', 'Statut', 'Date acquisition', 'Prix acquisition (MAD)']

        filename = get_export_filename('vehicules', format)

        if format == 'csv':
            return export_to_csv(vehicules, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(vehicules, headers, filename, 'Véhicules')
        elif format == 'xls':
            return export_to_excel_xls(vehicules, headers, filename, 'Véhicules')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('vehicules'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('vehicules'))

@gesparc_app.route('/export/conducteurs/<format>')
def export_conducteurs(format):
    """Exporter la liste des conducteurs"""
    try:
        conn = get_db_connection()
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        conn.close()

        headers = ['Nom', 'Prénom', 'Numéro permis', 'Date permis',
                  'Téléphone', 'Email', 'Statut']

        filename = get_export_filename('conducteurs', format)

        if format == 'csv':
            return export_to_csv(conducteurs, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(conducteurs, headers, filename, 'Conducteurs')
        elif format == 'xls':
            return export_to_excel_xls(conducteurs, headers, filename, 'Conducteurs')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('conducteurs'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('conducteurs'))

@gesparc_app.route('/export/maintenances/<format>')
def export_maintenances(format):
    """Exporter la liste des maintenances"""
    try:
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT m.type_maintenance, m.description, m.date_maintenance,
                   m.cout, m.garage, m.statut, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        conn.close()

        headers = ['Véhicule', 'Marque', 'Modèle', 'Type maintenance', 'Description',
                  'Date maintenance', 'Coût (MAD)', 'Garage', 'Statut']

        filename = get_export_filename('maintenances', format)

        if format == 'csv':
            return export_to_csv(maintenances, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(maintenances, headers, filename, 'Maintenances')
        elif format == 'xls':
            return export_to_excel_xls(maintenances, headers, filename, 'Maintenances')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('maintenances'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('maintenances'))

@gesparc_app.route('/export/affectations/<format>')
def export_affectations(format):
    """Exporter la liste des affectations"""
    try:
        conn = get_db_connection()
        affectations = conn.execute('''
            SELECT v.immatriculation, v.marque, v.modele, c.nom, c.prenom,
                   a.date_debut, a.date_fin, a.statut, a.commentaire
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        conn.close()

        headers = ['Véhicule', 'Marque', 'Modèle', 'Nom conducteur', 'Prénom conducteur',
                  'Date début', 'Date fin', 'Statut', 'Commentaire']

        filename = get_export_filename('affectations', format)

        if format == 'csv':
            return export_to_csv(affectations, headers, filename)
        elif format == 'xlsx':
            return export_to_excel_xlsx(affectations, headers, filename, 'Affectations')
        elif format == 'xls':
            return export_to_excel_xls(affectations, headers, filename, 'Affectations')
        else:
            flash('Format d\'export non supporté', 'error')
            return redirect(url_for('affectations'))

    except Exception as e:
        flash(f'Erreur lors de l\'export: {e}', 'error')
        return redirect(url_for('affectations'))

@gesparc_app.route('/export/complet/<format>')
def export_complet(format):
    """Exporter toutes les données dans un fichier multi-feuilles (Excel uniquement)"""
    try:
        if format not in ['xlsx', 'xls']:
            flash('Export complet disponible uniquement en format Excel', 'error')
            return redirect(url_for('rapports'))

        conn = get_db_connection()

        # Récupérer toutes les données
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        maintenances = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele, c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()

        conn.close()

        filename = get_export_filename('gesparc_complet', format)

        # Pour l'export complet, on utilise une fonction spécialisée
        return export_complet_excel(vehicules, conducteurs, maintenances, affectations, filename, format)

    except Exception as e:
        flash(f'Erreur lors de l\'export complet: {e}', 'error')
        return redirect(url_for('rapports'))

def export_complet_excel(vehicules, conducteurs, maintenances, affectations, filename, format):
    """Exporte toutes les données dans un fichier Excel multi-feuilles"""
    if format == 'xlsx':
        import openpyxl
        from openpyxl.styles import Font, PatternFill

        wb = openpyxl.Workbook()

        # Supprimer la feuille par défaut
        wb.remove(wb.active)

        # Feuille Véhicules
        ws_vehicules = wb.create_sheet("Véhicules")
        headers_vehicules = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Couleur',
                           'Kilométrage', 'Carburant', 'Statut', 'Date acquisition', 'Prix acquisition (MAD)']
        add_data_to_sheet(ws_vehicules, vehicules, headers_vehicules)

        # Feuille Conducteurs
        ws_conducteurs = wb.create_sheet("Conducteurs")
        headers_conducteurs = ['Nom', 'Prénom', 'Numéro permis', 'Date permis',
                             'Téléphone', 'Email', 'Statut']
        add_data_to_sheet(ws_conducteurs, conducteurs, headers_conducteurs)

        # Feuille Maintenances
        ws_maintenances = wb.create_sheet("Maintenances")
        headers_maintenances = ['Véhicule', 'Type maintenance', 'Description',
                              'Date maintenance', 'Coût (MAD)', 'Garage', 'Statut']
        add_data_to_sheet(ws_maintenances, maintenances, headers_maintenances)

        # Feuille Affectations
        ws_affectations = wb.create_sheet("Affectations")
        headers_affectations = ['Véhicule', 'Conducteur', 'Date début', 'Date fin', 'Statut', 'Commentaire']
        add_data_to_sheet(ws_affectations, affectations, headers_affectations)

        # Sauvegarder
        import io
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename={filename}.xlsx'}
        )

    else:  # format == 'xls'
        # Pour XLS, on fait un export simple de la première feuille seulement
        headers = ['Immatriculation', 'Marque', 'Modèle', 'Année', 'Carburant', 'Statut']
        return export_to_excel_xls(vehicules, headers, filename, 'Véhicules')

def add_data_to_sheet(worksheet, data, headers):
    """Ajoute des données à une feuille Excel avec formatage"""
    from openpyxl.styles import Font, PatternFill, Alignment

    # Styles pour les en-têtes
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    # Écrire les en-têtes
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    # Écrire les données
    for row_idx, row in enumerate(data, 2):
        for col_idx, header in enumerate(headers, 1):
            if isinstance(row, dict):
                key = header.lower().replace(' ', '_').replace('é', 'e').replace('è', 'e')
                value = row.get(key, '')
            else:
                value = row[col_idx - 1] if col_idx - 1 < len(row) else ''
            worksheet.cell(row=row_idx, column=col_idx, value=value)

    # Ajuster la largeur des colonnes
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

# Routes Analytics Matplotlib
@gesparc_app.route('/analytics/matplotlib')
def analytics_matplotlib():
    """Page des analytics avec graphiques Matplotlib"""
    try:
        analytics = GesparcAnalytics()

        # Générer tous les graphiques
        charts = analytics.create_comprehensive_report()

        return render_template('analytics_matplotlib.html', charts=charts)

    except Exception as e:
        flash(f'Erreur lors de la génération des analytics: {e}', 'error')
        return render_template('analytics_matplotlib.html', charts={})

@gesparc_app.route('/api/analytics/chart/<chart_type>')
def api_analytics_chart(chart_type):
    """API pour générer un graphique spécifique"""
    try:
        analytics = GesparcAnalytics()

        if chart_type == 'evolution':
            period = request.args.get('period', 12, type=int)
            chart_data = analytics.create_maintenance_evolution_chart(period)
        elif chart_type == 'dashboard':
            chart_data = analytics.create_vehicle_analysis_dashboard()
        elif chart_type == 'costs':
            chart_data = analytics.create_maintenance_cost_analysis()
        elif chart_type == 'heatmap':
            chart_data = analytics.create_performance_heatmap()
        elif chart_type == 'predictive':
            chart_data = analytics.create_predictive_analysis()
        else:
            return jsonify({'error': 'Type de graphique non supporté'}), 400

        return jsonify({
            'success': True,
            'chart_data': chart_data,
            'chart_type': chart_type
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@gesparc_app.route('/analytics/download/<chart_type>')
def download_analytics_chart(chart_type):
    """Télécharger un graphique en PNG"""
    try:
        analytics = GesparcAnalytics()

        if chart_type == 'evolution':
            chart_data = analytics.create_maintenance_evolution_chart()
        elif chart_type == 'dashboard':
            chart_data = analytics.create_vehicle_analysis_dashboard()
        elif chart_type == 'costs':
            chart_data = analytics.create_maintenance_cost_analysis()
        elif chart_type == 'heatmap':
            chart_data = analytics.create_performance_heatmap()
        elif chart_type == 'predictive':
            chart_data = analytics.create_predictive_analysis()
        else:
            flash('Type de graphique non supporté', 'error')
            return redirect(url_for('analytics_matplotlib'))

        # Décoder le base64 et créer la réponse
        import base64
        img_data = base64.b64decode(chart_data)

        response = make_response(img_data)
        response.headers['Content-Type'] = 'image/png'
        response.headers['Content-Disposition'] = f'attachment; filename=gesparc_analytics_{chart_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'

        return response

    except Exception as e:
        flash(f'Erreur lors du téléchargement: {e}', 'error')
        return redirect(url_for('analytics_matplotlib'))

@gesparc_app.route('/test-prefix')
def test_prefix():
    """Page de test pour vérifier la configuration du préfixe"""
    return render_template('test_prefix.html')

@gesparc_app.route('/api/prefix-info')
def api_prefix_info():
    """API pour obtenir les informations de configuration du préfixe"""
    return jsonify({
        'use_prefix': USE_PREFIX,
        'prefix': GESPARC_PREFIX if USE_PREFIX else None,
        'application_root': gesparc_app.config.get('APPLICATION_ROOT'),
        'script_name': os.environ.get('SCRIPT_NAME'),
        'request_url': request.url,
        'request_path': request.path,
        'request_script_root': request.script_root,
        'urls': {
            'index': url_for('index'),
            'vehicules': url_for('vehicules'),
            'analytics': url_for('analytics_matplotlib'),
            'rapports': url_for('rapports'),
            'test_prefix': url_for('test_prefix')
        },
        'urls_with_prefix': {
            'index': url_for_prefix('index'),
            'vehicules': url_for_prefix('vehicules'),
            'analytics': url_for_prefix('analytics_matplotlib'),
            'rapports': url_for_prefix('rapports'),
            'test_prefix': url_for_prefix('test_prefix')
        } if USE_PREFIX else None
    })



if __name__ == '__main__':
    print("=" * 50)
    print("🚗 GesParc Auto - Gestion de Parc Automobile")
    print("=" * 50)
    print("Démarrage de l'application...")
    print("Application disponible sur: http://localhost:8080")
    if USE_PREFIX:
        print("Via Apache: http://localhost/gesparc")
    print("Appuyez sur Ctrl+C pour arrêter l'application")
    print("=" * 50)

    gesparc_app.run(debug=True, host='127.0.0.1', port=8080)

#!/usr/bin/env python3
"""
Module de génération de graphiques avec Matplotlib pour GesParc Auto
Créé des analyses visuelles avancées pour le parc automobile
"""

import matplotlib
matplotlib.use('Agg')  # Backend non-interactif pour serveur web

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
import io
import base64
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class GesparcAnalytics:
    """Générateur de graphiques analytics pour GesParc Auto"""
    
    def __init__(self, db_path: str = 'parc_automobile.db'):
        """
        Initialise le générateur d'analytics
        
        Args:
            db_path: Chemin vers la base de données SQLite
        """
        self.db_path = db_path
        self.setup_style()
        
    def setup_style(self):
        """Configure le style global des graphiques"""
        # Style Seaborn moderne
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        # Configuration matplotlib
        plt.rcParams.update({
            'figure.figsize': (12, 8),
            'figure.dpi': 100,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'axes.titlesize': 16,
            'axes.labelsize': 12,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 11,
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
            'axes.grid': True,
            'grid.alpha': 0.3,
            'axes.spines.top': False,
            'axes.spines.right': False,
        })
        
        # Palette de couleurs GesParc
        self.colors = {
            'primary': '#007bff',
            'success': '#28a745', 
            'warning': '#ffc107',
            'danger': '#dc3545',
            'info': '#17a2b8',
            'secondary': '#6c757d',
            'palette': ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1', '#fd7e14']
        }
        
    def get_data(self) -> Dict:
        """Récupère toutes les données nécessaires depuis la base"""
        conn = sqlite3.connect(self.db_path)
        
        data = {}
        
        # Véhicules (adapter aux colonnes existantes)
        data['vehicules'] = pd.read_sql_query('''
            SELECT id, immatriculation, marque, modele, annee, statut, carburant, kilometrage
            FROM vehicules
        ''', conn)
        
        # Maintenances
        data['maintenances'] = pd.read_sql_query('''
            SELECT m.*, v.immatriculation, v.marque, v.modele
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
        ''', conn)
        
        # Conducteurs et affectations
        data['conducteurs'] = pd.read_sql_query('''
            SELECT c.*, a.vehicule_id, a.date_debut, a.date_fin, a.statut as statut_affectation,
                   v.immatriculation
            FROM conducteurs c
            LEFT JOIN affectations a ON c.id = a.conducteur_id
            LEFT JOIN vehicules v ON a.vehicule_id = v.id
        ''', conn)
        
        conn.close()
        
        # Conversion des dates
        if not data['maintenances'].empty:
            data['maintenances']['date_maintenance'] = pd.to_datetime(data['maintenances']['date_maintenance'])
            data['maintenances']['date_creation'] = pd.to_datetime(data['maintenances']['date_creation'])
        
        # Pas de conversion de date_achat car la colonne n'existe pas
            
        return data
        
    def create_maintenance_evolution_chart(self, period_months: int = 12) -> str:
        """
        Crée un graphique d'évolution des maintenances sur une période
        
        Args:
            period_months: Nombre de mois à analyser
            
        Returns:
            String base64 de l'image du graphique
        """
        data = self.get_data()
        
        if data['maintenances'].empty:
            return self._create_no_data_chart("Aucune donnée de maintenance disponible")
        
        # Filtrer par période
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period_months * 30)
        
        df = data['maintenances'][
            data['maintenances']['date_maintenance'] >= start_date
        ].copy()
        
        if df.empty:
            return self._create_no_data_chart(f"Aucune maintenance sur les {period_months} derniers mois")
        
        # Grouper par mois
        df['mois'] = df['date_maintenance'].dt.to_period('M')
        monthly_stats = df.groupby('mois').agg({
            'id': 'count',
            'cout': ['sum', 'mean']
        }).round(2)
        
        monthly_stats.columns = ['nb_maintenances', 'cout_total', 'cout_moyen']
        monthly_stats = monthly_stats.reset_index()
        monthly_stats['mois_str'] = monthly_stats['mois'].astype(str)
        
        # Création du graphique
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        fig.suptitle('Évolution des Maintenances - Analyse Temporelle', fontsize=18, fontweight='bold')
        
        # Graphique 1: Nombre de maintenances
        ax1.plot(monthly_stats['mois_str'], monthly_stats['nb_maintenances'], 
                marker='o', linewidth=3, markersize=8, color=self.colors['primary'])
        ax1.fill_between(monthly_stats['mois_str'], monthly_stats['nb_maintenances'], 
                        alpha=0.3, color=self.colors['primary'])
        ax1.set_title('Nombre de Maintenances par Mois', fontsize=14, pad=20)
        ax1.set_ylabel('Nombre de maintenances', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # Rotation des labels
        ax1.tick_params(axis='x', rotation=45)
        
        # Graphique 2: Coûts
        ax2_twin = ax2.twinx()
        
        bars = ax2.bar(monthly_stats['mois_str'], monthly_stats['cout_total'], 
                      alpha=0.7, color=self.colors['success'], label='Coût total')
        line = ax2_twin.plot(monthly_stats['mois_str'], monthly_stats['cout_moyen'], 
                           color=self.colors['danger'], marker='s', linewidth=2, 
                           markersize=6, label='Coût moyen')
        
        ax2.set_title('Évolution des Coûts de Maintenance', fontsize=14, pad=20)
        ax2.set_ylabel('Coût total (MAD)', fontsize=12, color=self.colors['success'])
        ax2_twin.set_ylabel('Coût moyen (MAD)', fontsize=12, color=self.colors['danger'])
        ax2.tick_params(axis='x', rotation=45)
        
        # Légendes
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()
        ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def create_vehicle_analysis_dashboard(self) -> str:
        """
        Crée un dashboard d'analyse des véhicules
        
        Returns:
            String base64 de l'image du dashboard
        """
        data = self.get_data()
        
        if data['vehicules'].empty:
            return self._create_no_data_chart("Aucune donnée de véhicule disponible")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Dashboard d\'Analyse des Véhicules', fontsize=20, fontweight='bold')
        
        # 1. Répartition par statut (Pie chart)
        statut_counts = data['vehicules']['statut'].value_counts()
        colors_pie = [self.colors['palette'][i % len(self.colors['palette'])] for i in range(len(statut_counts))]
        
        wedges, texts, autotexts = ax1.pie(statut_counts.values, labels=statut_counts.index, 
                                          autopct='%1.1f%%', colors=colors_pie, startangle=90)
        ax1.set_title('Répartition par Statut', fontsize=14, pad=20)
        
        # Améliorer l'apparence du pie chart
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        # 2. Répartition par carburant (Bar chart)
        if 'carburant' in data['vehicules'].columns:
            carburant_counts = data['vehicules']['carburant'].value_counts()
            bars = ax2.bar(carburant_counts.index, carburant_counts.values, 
                          color=self.colors['palette'][:len(carburant_counts)])
            ax2.set_title('Répartition par Type de Carburant', fontsize=14, pad=20)
            ax2.set_ylabel('Nombre de véhicules')
            ax2.tick_params(axis='x', rotation=45)
            
            # Ajouter les valeurs sur les barres
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        # 3. Distribution des âges (Histogram)
        if 'annee' in data['vehicules'].columns:
            ages = 2025 - data['vehicules']['annee']
            ax3.hist(ages, bins=10, alpha=0.7, color=self.colors['info'], edgecolor='black')
            ax3.set_title('Distribution des Âges des Véhicules', fontsize=14, pad=20)
            ax3.set_xlabel('Âge (années)')
            ax3.set_ylabel('Nombre de véhicules')
            ax3.axvline(ages.mean(), color=self.colors['danger'], linestyle='--', 
                       linewidth=2, label=f'Âge moyen: {ages.mean():.1f} ans')
            ax3.legend()
        
        # 4. Top marques (Horizontal bar)
        if 'marque' in data['vehicules'].columns:
            marque_counts = data['vehicules']['marque'].value_counts().head(8)
            y_pos = np.arange(len(marque_counts))
            bars = ax4.barh(y_pos, marque_counts.values, color=self.colors['warning'])
            ax4.set_yticks(y_pos)
            ax4.set_yticklabels(marque_counts.index)
            ax4.set_title('Top Marques de Véhicules', fontsize=14, pad=20)
            ax4.set_xlabel('Nombre de véhicules')
            
            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax4.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{int(width)}', ha='left', va='center', fontweight='bold')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def create_maintenance_cost_analysis(self) -> str:
        """
        Analyse détaillée des coûts de maintenance
        
        Returns:
            String base64 de l'image de l'analyse
        """
        data = self.get_data()
        
        if data['maintenances'].empty:
            return self._create_no_data_chart("Aucune donnée de maintenance disponible")
        
        # Filtrer les maintenances avec coût
        df = data['maintenances'][data['maintenances']['cout'].notna() & (data['maintenances']['cout'] > 0)].copy()
        
        if df.empty:
            return self._create_no_data_chart("Aucune donnée de coût disponible")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Analyse des Coûts de Maintenance', fontsize=20, fontweight='bold')
        
        # 1. Distribution des coûts (Histogram avec KDE)
        ax1.hist(df['cout'], bins=20, alpha=0.7, color=self.colors['primary'], density=True)
        
        # Ajouter une courbe de densité
        from scipy import stats
        kde = stats.gaussian_kde(df['cout'])
        x_range = np.linspace(df['cout'].min(), df['cout'].max(), 100)
        ax1.plot(x_range, kde(x_range), color=self.colors['danger'], linewidth=2, label='Densité')
        
        ax1.set_title('Distribution des Coûts de Maintenance', fontsize=14, pad=20)
        ax1.set_xlabel('Coût (MAD)')
        ax1.set_ylabel('Densité')
        ax1.legend()
        
        # 2. Coûts par type de maintenance (Box plot)
        if 'type_maintenance' in df.columns:
            types = df['type_maintenance'].unique()
            data_for_box = [df[df['type_maintenance'] == t]['cout'].values for t in types]
            
            box_plot = ax2.boxplot(data_for_box, labels=types, patch_artist=True)
            
            # Colorer les boîtes
            for patch, color in zip(box_plot['boxes'], self.colors['palette']):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax2.set_title('Coûts par Type de Maintenance', fontsize=14, pad=20)
            ax2.set_ylabel('Coût (MAD)')
            ax2.tick_params(axis='x', rotation=45)
        
        # 3. Évolution des coûts dans le temps (Scatter plot avec tendance)
        df_sorted = df.sort_values('date_maintenance')
        ax3.scatter(df_sorted['date_maintenance'], df_sorted['cout'], 
                   alpha=0.6, color=self.colors['info'], s=50)
        
        # Ligne de tendance
        x_numeric = mdates.date2num(df_sorted['date_maintenance'])
        z = np.polyfit(x_numeric, df_sorted['cout'], 1)
        p = np.poly1d(z)
        ax3.plot(df_sorted['date_maintenance'], p(x_numeric), 
                color=self.colors['danger'], linewidth=2, linestyle='--', label='Tendance')
        
        ax3.set_title('Évolution des Coûts dans le Temps', fontsize=14, pad=20)
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Coût (MAD)')
        ax3.legend()
        
        # Format des dates
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Top véhicules par coût total (Bar chart)
        cout_par_vehicule = df.groupby('immatriculation')['cout'].sum().sort_values(ascending=False).head(8)
        
        bars = ax4.bar(range(len(cout_par_vehicule)), cout_par_vehicule.values, 
                      color=self.colors['warning'])
        ax4.set_xticks(range(len(cout_par_vehicule)))
        ax4.set_xticklabels(cout_par_vehicule.index, rotation=45)
        ax4.set_title('Top Véhicules par Coût Total', fontsize=14, pad=20)
        ax4.set_ylabel('Coût total (MAD)')
        
        # Ajouter les valeurs sur les barres
        for bar in bars:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
        
    def _create_no_data_chart(self, message: str) -> str:
        """Crée un graphique indiquant l'absence de données"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=16, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['secondary'], alpha=0.3))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._fig_to_base64(fig)
        
    def _fig_to_base64(self, fig) -> str:
        """Convertit une figure matplotlib en string base64"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', bbox_inches='tight', dpi=300)
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close(fig)  # Libérer la mémoire
        return img_str

    def create_performance_heatmap(self) -> str:
        """
        Crée une heatmap de performance des véhicules

        Returns:
            String base64 de l'image de la heatmap
        """
        data = self.get_data()

        if data['vehicules'].empty or data['maintenances'].empty:
            return self._create_no_data_chart("Données insuffisantes pour la heatmap")

        # Créer une matrice de performance
        vehicules_stats = []

        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]

            stats = {
                'Immatriculation': vehicule['immatriculation'],
                'Âge': 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0,
                'Kilométrage': vehicule['kilometrage'] / 1000 if pd.notna(vehicule['kilometrage']) else 0,  # En milliers
                'Nb_Maintenances': len(maintenances_vehicule),
                'Coût_Total': maintenances_vehicule['cout'].sum() if not maintenances_vehicule.empty else 0,
                'Coût_Moyen': maintenances_vehicule['cout'].mean() if not maintenances_vehicule.empty else 0
            }
            vehicules_stats.append(stats)

        df_stats = pd.DataFrame(vehicules_stats)

        if df_stats.empty:
            return self._create_no_data_chart("Aucune statistique calculable")

        # Préparer les données pour la heatmap
        df_heatmap = df_stats.set_index('Immatriculation')[
            ['Âge', 'Kilométrage', 'Nb_Maintenances', 'Coût_Total', 'Coût_Moyen']
        ].fillna(0)

        # Normaliser les données (0-1)
        df_normalized = (df_heatmap - df_heatmap.min()) / (df_heatmap.max() - df_heatmap.min())
        df_normalized = df_normalized.fillna(0)

        # Créer la heatmap
        fig, ax = plt.subplots(figsize=(12, 8))

        sns.heatmap(df_normalized.T, annot=True, cmap='RdYlBu_r', center=0.5,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

        ax.set_title('Heatmap de Performance des Véhicules\n(Valeurs normalisées 0-1)',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Véhicules', fontsize=12)
        ax.set_ylabel('Métriques de Performance', fontsize=12)

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_predictive_analysis(self) -> str:
        """
        Analyse prédictive des maintenances

        Returns:
            String base64 de l'image de l'analyse prédictive
        """
        data = self.get_data()

        if data['maintenances'].empty:
            return self._create_no_data_chart("Données insuffisantes pour l'analyse prédictive")

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Analyse Prédictive des Maintenances', fontsize=20, fontweight='bold')

        # 1. Corrélation Âge vs Coût de maintenance
        vehicules_avec_cout = []
        for _, vehicule in data['vehicules'].iterrows():
            maintenances_vehicule = data['maintenances'][
                data['maintenances']['vehicule_id'] == vehicule['id']
            ]
            if not maintenances_vehicule.empty and maintenances_vehicule['cout'].notna().any():
                age = 2025 - vehicule['annee'] if pd.notna(vehicule['annee']) else 0
                cout_total = maintenances_vehicule['cout'].sum()
                vehicules_avec_cout.append({'age': age, 'cout_total': cout_total})

        if vehicules_avec_cout:
            df_corr = pd.DataFrame(vehicules_avec_cout)
            ax1.scatter(df_corr['age'], df_corr['cout_total'], alpha=0.7,
                       color=self.colors['primary'], s=60)

            # Ligne de tendance
            if len(df_corr) > 1:
                z = np.polyfit(df_corr['age'], df_corr['cout_total'], 1)
                p = np.poly1d(z)
                ax1.plot(df_corr['age'], p(df_corr['age']),
                        color=self.colors['danger'], linewidth=2, linestyle='--')

                # Calculer R²
                correlation = np.corrcoef(df_corr['age'], df_corr['cout_total'])[0,1]
                ax1.text(0.05, 0.95, f'Corrélation: {correlation:.3f}',
                        transform=ax1.transAxes, fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            ax1.set_title('Corrélation Âge vs Coût Total', fontsize=14, pad=20)
            ax1.set_xlabel('Âge du véhicule (années)')
            ax1.set_ylabel('Coût total maintenance (MAD)')

        # 2. Fréquence des maintenances par mois
        if not data['maintenances'].empty:
            df_maint = data['maintenances'].copy()
            df_maint['mois'] = df_maint['date_maintenance'].dt.month
            freq_mois = df_maint['mois'].value_counts().sort_index()

            mois_noms = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                        'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc']

            bars = ax2.bar(range(1, 13), [freq_mois.get(i, 0) for i in range(1, 13)],
                          color=self.colors['success'], alpha=0.7)
            ax2.set_xticks(range(1, 13))
            ax2.set_xticklabels(mois_noms, rotation=45)
            ax2.set_title('Saisonnalité des Maintenances', fontsize=14, pad=20)
            ax2.set_ylabel('Nombre de maintenances')

            # Ajouter les valeurs
            for i, bar in enumerate(bars):
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        # 3. Prédiction des prochaines maintenances (basée sur le kilométrage)
        vehicules_prediction = []
        for _, vehicule in data['vehicules'].iterrows():
            if pd.notna(vehicule['kilometrage']) and vehicule['kilometrage'] > 0:
                # Estimation simple: maintenance tous les 10000 km
                km_actuel = vehicule['kilometrage']
                prochaine_maintenance = ((km_actuel // 10000) + 1) * 10000
                km_restants = prochaine_maintenance - km_actuel

                vehicules_prediction.append({
                    'immatriculation': vehicule['immatriculation'],
                    'km_restants': km_restants,
                    'urgence': 'Urgent' if km_restants < 2000 else 'Moyen' if km_restants < 5000 else 'Normal'
                })

        if vehicules_prediction:
            df_pred = pd.DataFrame(vehicules_prediction)
            urgence_counts = df_pred['urgence'].value_counts()

            colors_urgence = {'Urgent': self.colors['danger'],
                            'Moyen': self.colors['warning'],
                            'Normal': self.colors['success']}
            colors_pie = [colors_urgence.get(cat, self.colors['secondary']) for cat in urgence_counts.index]

            wedges, texts, autotexts = ax3.pie(urgence_counts.values, labels=urgence_counts.index,
                                              autopct='%1.1f%%', colors=colors_pie, startangle=90)
            ax3.set_title('Urgence des Prochaines Maintenances', fontsize=14, pad=20)

            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        # 4. Tendance des coûts moyens
        if not data['maintenances'].empty:
            df_maint = data['maintenances'][data['maintenances']['cout'].notna()].copy()
            if not df_maint.empty:
                df_maint['trimestre'] = df_maint['date_maintenance'].dt.to_period('Q')
                cout_trimestre = df_maint.groupby('trimestre')['cout'].mean()

                ax4.plot(range(len(cout_trimestre)), cout_trimestre.values,
                        marker='o', linewidth=3, markersize=8, color=self.colors['info'])
                ax4.fill_between(range(len(cout_trimestre)), cout_trimestre.values,
                               alpha=0.3, color=self.colors['info'])

                ax4.set_title('Évolution du Coût Moyen par Trimestre', fontsize=14, pad=20)
                ax4.set_ylabel('Coût moyen (MAD)')
                ax4.set_xlabel('Trimestre')
                ax4.set_xticks(range(len(cout_trimestre)))
                ax4.set_xticklabels([str(q) for q in cout_trimestre.index], rotation=45)

        plt.tight_layout()
        return self._fig_to_base64(fig)

    def create_comprehensive_report(self) -> Dict[str, str]:
        """
        Génère un rapport complet avec tous les graphiques

        Returns:
            Dictionnaire avec les graphiques en base64
        """
        report = {}

        try:
            report['evolution'] = self.create_maintenance_evolution_chart()
        except Exception as e:
            print(f"Erreur évolution: {e}")
            report['evolution'] = self._create_no_data_chart("Erreur génération évolution")

        try:
            report['dashboard'] = self.create_vehicle_analysis_dashboard()
        except Exception as e:
            print(f"Erreur dashboard: {e}")
            report['dashboard'] = self._create_no_data_chart("Erreur génération dashboard")

        try:
            report['costs'] = self.create_maintenance_cost_analysis()
        except Exception as e:
            print(f"Erreur coûts: {e}")
            report['costs'] = self._create_no_data_chart("Erreur génération analyse coûts")

        try:
            report['heatmap'] = self.create_performance_heatmap()
        except Exception as e:
            print(f"Erreur heatmap: {e}")
            report['heatmap'] = self._create_no_data_chart("Erreur génération heatmap")

        try:
            report['predictive'] = self.create_predictive_analysis()
        except Exception as e:
            print(f"Erreur prédictive: {e}")
            report['predictive'] = self._create_no_data_chart("Erreur génération analyse prédictive")

        return report

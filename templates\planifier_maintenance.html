{% extends "base.html" %}

{% block title %}Planifier une Maintenance - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tools"></i> Planifier une Maintenance</h1>
            <a href="{{ url_for('maintenances') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour aux maintenances
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-plus"></i> Nouvelle maintenance
                </h5>
            </div>
            <div class="card-body">
                {% if not vehicules %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Aucun véhicule disponible</strong><br>
                    V<PERSON> devez d'abord ajouter des véhicules avant de pouvoir planifier des maintenances.
                    <a href="{{ url_for('ajouter_vehicule') }}" class="btn btn-sm btn-outline-primary ms-2">
                        Ajouter un véhicule
                    </a>
                </div>
                {% else %}
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Véhicule -->
                        <div class="col-md-6 mb-3">
                            <label for="vehicule_id" class="form-label">
                                <i class="fas fa-car"></i> Véhicule *
                            </label>
                            <select class="form-select" id="vehicule_id" name="vehicule_id" required>
                                <option value="">Sélectionner un véhicule</option>
                                {% for vehicule in vehicules %}
                                <option value="{{ vehicule.id }}" 
                                        data-immatriculation="{{ vehicule.immatriculation }}"
                                        data-marque="{{ vehicule.marque }}"
                                        data-modele="{{ vehicule.modele }}"
                                        data-annee="{{ vehicule.annee }}"
                                        data-kilometrage="{{ vehicule.kilometrage }}">
                                    {{ vehicule.immatriculation }} - {{ vehicule.marque }} {{ vehicule.modele }} ({{ vehicule.annee }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner un véhicule
                            </div>
                            <div id="vehicule-info" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="vehicule-details"></span>
                                </small>
                            </div>
                        </div>

                        <!-- Type de maintenance -->
                        <div class="col-md-6 mb-3">
                            <label for="type_maintenance" class="form-label">
                                <i class="fas fa-wrench"></i> Type de maintenance *
                            </label>
                            <select class="form-select" id="type_maintenance" name="type_maintenance" required>
                                <option value="">Sélectionner le type</option>
                                <option value="Vidange">Vidange</option>
                                <option value="Révision">Révision</option>
                                <option value="Contrôle technique">Contrôle technique</option>
                                <option value="Changement pneus">Changement pneus</option>
                                <option value="Freinage">Freinage</option>
                                <option value="Climatisation">Climatisation</option>
                                <option value="Batterie">Batterie</option>
                                <option value="Carrosserie">Carrosserie</option>
                                <option value="Électricité">Électricité</option>
                                <option value="Transmission">Transmission</option>
                                <option value="Suspension">Suspension</option>
                                <option value="Autre">Autre</option>
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner le type de maintenance
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date de maintenance -->
                        <div class="col-md-6 mb-3">
                            <label for="date_maintenance" class="form-label">
                                <i class="fas fa-calendar"></i> Date prévue *
                            </label>
                            <input type="date" class="form-control" id="date_maintenance" 
                                   name="date_maintenance" required
                                   min="{{ date_today }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la date de maintenance
                            </div>
                        </div>

                        <!-- Priorité -->
                        <div class="col-md-6 mb-3">
                            <label for="priorite" class="form-label">
                                <i class="fas fa-exclamation-circle"></i> Priorité
                            </label>
                            <select class="form-select" id="priorite" name="priorite">
                                <option value="faible">🟢 Faible</option>
                                <option value="normale" selected>🟡 Normale</option>
                                <option value="elevee">🟠 Élevée</option>
                                <option value="urgente">🔴 Urgente</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Coût estimé -->
                        <div class="col-md-6 mb-3">
                            <label for="cout_estime" class="form-label">
                                <i class="fas fa-coins"></i> Coût estimé (MAD)
                            </label>
                            <input type="number" class="form-control" id="cout_estime" 
                                   name="cout_estime" min="0" step="0.01"
                                   placeholder="0.00">
                            <small class="form-text text-muted">
                                Estimation du coût de la maintenance
                            </small>
                        </div>

                        <!-- Garage -->
                        <div class="col-md-6 mb-3">
                            <label for="garage" class="form-label">
                                <i class="fas fa-store"></i> Garage / Prestataire
                            </label>
                            <input type="text" class="form-control" id="garage" 
                                   name="garage" placeholder="Nom du garage ou prestataire">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-comment"></i> Description / Notes
                            </label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="Détails de la maintenance, pièces à changer, observations..."></textarea>
                        </div>
                    </div>

                    <!-- Statut -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-flag"></i> Statut
                            </label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="planifiee" selected>📅 Planifiée</option>
                                <option value="en_cours">⚙️ En cours</option>
                                <option value="terminee">✅ Terminée</option>
                                <option value="reportee">⏸️ Reportée</option>
                                <option value="annulee">❌ Annulée</option>
                            </select>
                        </div>
                    </div>

                    <!-- Résumé de la maintenance -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info" id="resume-maintenance" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Résumé de la maintenance</h6>
                                <div id="resume-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('maintenances') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Planifier la maintenance
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Informations utiles -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-lightbulb"></i> Conseils</h6>
                <ul class="small mb-0">
                    <li>Planifiez les vidanges tous les 10 000 km</li>
                    <li>Contrôle technique annuel obligatoire</li>
                    <li>Vérifiez les pneus régulièrement</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-clock"></i> Fréquences recommandées</h6>
                <ul class="small mb-0">
                    <li>Vidange : 6 mois ou 10 000 km</li>
                    <li>Révision : 12 mois ou 20 000 km</li>
                    <li>Pneus : Selon usure</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6><i class="fas fa-chart-line"></i> Statistiques</h6>
                <p class="small mb-0">
                    <strong>{{ vehicules|length }}</strong> véhicule(s) dans le parc<br>
                    Maintenances à planifier selon les besoins
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Affichage des détails du véhicule
    const vehiculeSelect = document.getElementById('vehicule_id');
    const vehiculeInfo = document.getElementById('vehicule-info');
    const vehiculeDetails = document.getElementById('vehicule-details');

    vehiculeSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const immatriculation = option.dataset.immatriculation;
            const marque = option.dataset.marque;
            const modele = option.dataset.modele;
            const annee = option.dataset.annee;
            const kilometrage = option.dataset.kilometrage;

            vehiculeDetails.innerHTML = `${marque} ${modele} (${annee}) - ${kilometrage} km`;
            vehiculeInfo.style.display = 'block';
        } else {
            vehiculeInfo.style.display = 'none';
        }
        updateResume();
    });

    // Mise à jour du résumé
    const typeSelect = document.getElementById('type_maintenance');
    const dateInput = document.getElementById('date_maintenance');
    const prioriteSelect = document.getElementById('priorite');
    const coutInput = document.getElementById('cout_estime');
    const garageInput = document.getElementById('garage');
    const statutSelect = document.getElementById('statut');

    [typeSelect, dateInput, prioriteSelect, coutInput, garageInput, statutSelect].forEach(element => {
        element.addEventListener('change', updateResume);
        element.addEventListener('input', updateResume);
    });

    function updateResume() {
        const vehiculeOption = vehiculeSelect.options[vehiculeSelect.selectedIndex];
        const type = typeSelect.value;
        const date = dateInput.value;
        const priorite = prioriteSelect.options[prioriteSelect.selectedIndex].text;
        const cout = coutInput.value;
        const garage = garageInput.value;
        const statut = statutSelect.options[statutSelect.selectedIndex].text;

        if (vehiculeOption.value && type && date) {
            const resumeDiv = document.getElementById('resume-maintenance');
            const resumeContent = document.getElementById('resume-content');

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Véhicule:</strong> ${vehiculeOption.text}<br>
                        <strong>Type:</strong> ${type}<br>
                        <strong>Date:</strong> ${formatDate(date)}
                    </div>
                    <div class="col-md-6">
                        <strong>Priorité:</strong> ${priorite}<br>
                        <strong>Statut:</strong> ${statut}
            `;

            if (cout) {
                html += `<br><strong>Coût estimé:</strong> ${cout} MAD`;
            }
            if (garage) {
                html += `<br><strong>Garage:</strong> ${garage}`;
            }

            html += `
                    </div>
                </div>
            `;

            resumeContent.innerHTML = html;
            resumeDiv.style.display = 'block';
        } else {
            document.getElementById('resume-maintenance').style.display = 'none';
        }
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Suggestions automatiques selon le type de maintenance
    typeSelect.addEventListener('change', function() {
        const type = this.value;
        const descriptionTextarea = document.getElementById('description');
        const coutInput = document.getElementById('cout_estime');

        // Suggestions de description et coût selon le type
        const suggestions = {
            'Vidange': {
                description: 'Vidange moteur + changement filtre à huile + vérification niveaux',
                cout: '300'
            },
            'Révision': {
                description: 'Révision complète selon carnet d\'entretien + contrôles sécurité',
                cout: '800'
            },
            'Contrôle technique': {
                description: 'Contrôle technique réglementaire + contre-visite si nécessaire',
                cout: '150'
            },
            'Changement pneus': {
                description: 'Changement pneus + équilibrage + géométrie',
                cout: '1200'
            },
            'Freinage': {
                description: 'Vérification système de freinage + changement plaquettes/disques si nécessaire',
                cout: '400'
            },
            'Climatisation': {
                description: 'Recharge climatisation + vérification circuit + désinfection',
                cout: '200'
            },
            'Batterie': {
                description: 'Test batterie + nettoyage bornes + remplacement si nécessaire',
                cout: '150'
            }
        };

        if (suggestions[type]) {
            if (!descriptionTextarea.value) {
                descriptionTextarea.value = suggestions[type].description;
            }
            if (!coutInput.value) {
                coutInput.value = suggestions[type].cout;
            }
        }

        updateResume();
    });

    // Définir la date d'aujourd'hui par défaut
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    if (!dateInput.value) {
        // Suggérer une date dans 1 semaine
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        dateInput.value = nextWeek.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}

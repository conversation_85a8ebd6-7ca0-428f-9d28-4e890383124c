{% extends "base.html" %}

{% block title %}Ajouter un Véhicule - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> Ajouter un Véhicule</h1>
            <a href="{{ url_for('vehicules') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car"></i> Informations du véhicule
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Immatriculation -->
                        <div class="col-md-6 mb-3">
                            <label for="immatriculation" class="form-label">
                                <i class="fas fa-id-card"></i> Immatriculation *
                                <small class="text-muted">(Format marocain)</small>
                            </label>
                            <input type="text" class="form-control" id="immatriculation"
                                   name="immatriculation" required
                                   placeholder="Ex: 12345-A-1, 123-A-1, 1-A-1, CD-123">
                            <div class="invalid-feedback" id="immat-feedback">
                                Veuillez saisir une immatriculation marocaine valide
                            </div>
                            <div class="valid-feedback" id="immat-success">
                                Immatriculation valide
                            </div>
                            <small class="form-text text-muted" id="immat-info">
                                Formats acceptés: Standard (12345-A-1), Court (123-A-1), Très court (1-A-1), Diplomatique (CD-123), etc.
                            </small>
                        </div>

                        <!-- Marque -->
                        <div class="col-md-6 mb-3">
                            <label for="marque" class="form-label">
                                <i class="fas fa-industry"></i> Marque *
                            </label>
                            <input type="text" class="form-control" id="marque" 
                                   name="marque" required
                                   placeholder="Peugeot, Renault, Citroën...">
                            <div class="invalid-feedback">
                                Veuillez saisir la marque du véhicule
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Modèle -->
                        <div class="col-md-6 mb-3">
                            <label for="modele" class="form-label">
                                <i class="fas fa-car-side"></i> Modèle *
                            </label>
                            <input type="text" class="form-control" id="modele" 
                                   name="modele" required
                                   placeholder="308, Clio, C3...">
                            <div class="invalid-feedback">
                                Veuillez saisir le modèle du véhicule
                            </div>
                        </div>

                        <!-- Année -->
                        <div class="col-md-6 mb-3">
                            <label for="annee" class="form-label">
                                <i class="fas fa-calendar"></i> Année *
                            </label>
                            <input type="number" class="form-control" id="annee" 
                                   name="annee" required
                                   min="1990" max="2025" value="2023">
                            <div class="invalid-feedback">
                                Veuillez saisir une année valide (1990-2025)
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Couleur -->
                        <div class="col-md-6 mb-3">
                            <label for="couleur" class="form-label">
                                <i class="fas fa-palette"></i> Couleur
                            </label>
                            <select class="form-select" id="couleur" name="couleur">
                                <option value="">Sélectionner une couleur</option>
                                <option value="Blanc">Blanc</option>
                                <option value="Noir">Noir</option>
                                <option value="Gris">Gris</option>
                                <option value="Rouge">Rouge</option>
                                <option value="Bleu">Bleu</option>
                                <option value="Vert">Vert</option>
                                <option value="Jaune">Jaune</option>
                                <option value="Orange">Orange</option>
                                <option value="Violet">Violet</option>
                                <option value="Marron">Marron</option>
                                <option value="Autre">Autre</option>
                            </select>
                        </div>

                        <!-- Carburant -->
                        <div class="col-md-6 mb-3">
                            <label for="carburant" class="form-label">
                                <i class="fas fa-gas-pump"></i> Carburant *
                            </label>
                            <select class="form-select" id="carburant" name="carburant" required>
                                <option value="">Sélectionner le carburant</option>
                                <option value="Essence">Essence</option>
                                <option value="Diesel">Diesel</option>
                                <option value="Hybride">Hybride</option>
                                <option value="Électrique">Électrique</option>
                                <option value="GPL">GPL</option>
                                <option value="GNV">GNV</option>
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner le type de carburant
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Kilométrage -->
                        <div class="col-md-6 mb-3">
                            <label for="kilometrage" class="form-label">
                                <i class="fas fa-tachometer-alt"></i> Kilométrage (km)
                            </label>
                            <input type="number" class="form-control" id="kilometrage" 
                                   name="kilometrage" min="0" value="0"
                                   placeholder="0">
                        </div>

                        <!-- Statut -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-info-circle"></i> Statut
                            </label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="disponible" selected>Disponible</option>
                                <option value="affecte">Affecté</option>
                                <option value="en_maintenance">En maintenance</option>
                                <option value="hors_service">Hors service</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date d'acquisition -->
                        <div class="col-md-6 mb-3">
                            <label for="date_acquisition" class="form-label">
                                <i class="fas fa-calendar-plus"></i> Date d'acquisition
                            </label>
                            <input type="date" class="form-control" id="date_acquisition" 
                                   name="date_acquisition">
                        </div>

                        <!-- Prix d'acquisition -->
                        <div class="col-md-6 mb-3">
                            <label for="prix_acquisition" class="form-label">
                                <i class="fas fa-coins"></i> Prix d'acquisition (MAD)
                            </label>
                            <input type="number" class="form-control" id="prix_acquisition" 
                                   name="prix_acquisition" min="0" step="0.01"
                                   placeholder="0.00">
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('vehicules') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Enregistrer le véhicule
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments pour la validation d'immatriculation
    const immatInput = document.getElementById('immatriculation');
    const immatFeedback = document.getElementById('immat-feedback');
    const immatSuccess = document.getElementById('immat-success');
    const immatInfo = document.getElementById('immat-info');

    let validationTimeout;

    // Validation en temps réel de l'immatriculation marocaine
    immatInput.addEventListener('input', function() {
        clearTimeout(validationTimeout);

        // Formatage automatique
        let value = this.value.toUpperCase().replace(/\s+/g, '-').replace(/[^0-9A-Z\-]/g, '');
        this.value = value;

        if (value.length < 3) {
            resetValidation();
            return;
        }

        // Délai pour éviter trop de requêtes
        validationTimeout = setTimeout(() => {
            validerImmatriculation(value);
        }, 500);
    });

    function validerImmatriculation(immatriculation) {
        fetch('/api/valider-immatriculation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                immatriculation: immatriculation
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valide) {
                // Immatriculation valide
                immatInput.classList.remove('is-invalid');
                immatInput.classList.add('is-valid');
                immatSuccess.textContent = `✅ ${data.message}`;

                // Afficher info régionale si disponible
                if (data.region) {
                    immatInfo.innerHTML = `
                        <i class="fas fa-map-marker-alt"></i>
                        Région: <strong>${data.region}</strong> -
                        Format: ${data.format.replace('_', ' ')}
                    `;
                    immatInfo.className = 'form-text text-success';
                } else {
                    immatInfo.innerHTML = `Format: ${data.format.replace('_', ' ')}`;
                    immatInfo.className = 'form-text text-success';
                }

                // Mettre à jour la valeur avec le format correct
                if (data.immatriculation_formatee !== immatriculation) {
                    immatInput.value = data.immatriculation_formatee;
                }

            } else {
                // Immatriculation invalide
                immatInput.classList.remove('is-valid');
                immatInput.classList.add('is-invalid');
                immatFeedback.textContent = `❌ ${data.message}`;
                immatInfo.innerHTML = 'Formats acceptés: Standard (12345-A-1), Court (123-A-1), Très court (1-A-1), Diplomatique (CD-123), etc.';
                immatInfo.className = 'form-text text-muted';
            }
        })
        .catch(error => {
            console.error('Erreur de validation:', error);
            resetValidation();
        });
    }

    function resetValidation() {
        immatInput.classList.remove('is-valid', 'is-invalid');
        immatInfo.innerHTML = 'Formats acceptés: Standard (12345-A-1), Court (123-A-1), Très court (1-A-1), Diplomatique (CD-123), etc.';
        immatInfo.className = 'form-text text-muted';
    }

    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity() || immatInput.classList.contains('is-invalid')) {
            event.preventDefault();
            event.stopPropagation();

            if (immatInput.classList.contains('is-invalid')) {
                immatInput.focus();
            }
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Modifier {{ vehicule.immatriculation }} - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> Modifier {{ vehicule.immatriculation }}</h1>
            <div class="btn-group">
                <a href="{{ url_for('voir_vehicule', id=vehicule.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour aux détails
                </a>
                <a href="{{ url_for('vehicules') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-list"></i> Liste des véhicules
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-car"></i> Informations du véhicule
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Immatriculation -->
                        <div class="col-md-6 mb-3">
                            <label for="immatriculation" class="form-label">
                                <i class="fas fa-id-card"></i> Immatriculation *
                            </label>
                            <input type="text" class="form-control" id="immatriculation" 
                                   name="immatriculation" required
                                   value="{{ vehicule.immatriculation }}"
                                   pattern="[A-Z]{2}-[0-9]{3}-[A-Z]{2}"
                                   title="Format: AB-123-CD">
                            <div class="invalid-feedback">
                                Veuillez saisir une immatriculation valide (format: AB-123-CD)
                            </div>
                        </div>

                        <!-- Marque -->
                        <div class="col-md-6 mb-3">
                            <label for="marque" class="form-label">
                                <i class="fas fa-industry"></i> Marque *
                            </label>
                            <input type="text" class="form-control" id="marque" 
                                   name="marque" required
                                   value="{{ vehicule.marque }}">
                            <div class="invalid-feedback">
                                Veuillez saisir la marque du véhicule
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Modèle -->
                        <div class="col-md-6 mb-3">
                            <label for="modele" class="form-label">
                                <i class="fas fa-car-side"></i> Modèle *
                            </label>
                            <input type="text" class="form-control" id="modele" 
                                   name="modele" required
                                   value="{{ vehicule.modele }}">
                            <div class="invalid-feedback">
                                Veuillez saisir le modèle du véhicule
                            </div>
                        </div>

                        <!-- Année -->
                        <div class="col-md-6 mb-3">
                            <label for="annee" class="form-label">
                                <i class="fas fa-calendar"></i> Année *
                            </label>
                            <input type="number" class="form-control" id="annee" 
                                   name="annee" required
                                   min="1990" max="2025" 
                                   value="{{ vehicule.annee }}">
                            <div class="invalid-feedback">
                                Veuillez saisir une année valide (1990-2025)
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Couleur -->
                        <div class="col-md-6 mb-3">
                            <label for="couleur" class="form-label">
                                <i class="fas fa-palette"></i> Couleur
                            </label>
                            <select class="form-select" id="couleur" name="couleur">
                                <option value="">Sélectionner une couleur</option>
                                <option value="Blanc" {{ 'selected' if vehicule.couleur == 'Blanc' }}>Blanc</option>
                                <option value="Noir" {{ 'selected' if vehicule.couleur == 'Noir' }}>Noir</option>
                                <option value="Gris" {{ 'selected' if vehicule.couleur == 'Gris' }}>Gris</option>
                                <option value="Rouge" {{ 'selected' if vehicule.couleur == 'Rouge' }}>Rouge</option>
                                <option value="Bleu" {{ 'selected' if vehicule.couleur == 'Bleu' }}>Bleu</option>
                                <option value="Vert" {{ 'selected' if vehicule.couleur == 'Vert' }}>Vert</option>
                                <option value="Jaune" {{ 'selected' if vehicule.couleur == 'Jaune' }}>Jaune</option>
                                <option value="Orange" {{ 'selected' if vehicule.couleur == 'Orange' }}>Orange</option>
                                <option value="Violet" {{ 'selected' if vehicule.couleur == 'Violet' }}>Violet</option>
                                <option value="Marron" {{ 'selected' if vehicule.couleur == 'Marron' }}>Marron</option>
                                <option value="Autre" {{ 'selected' if vehicule.couleur == 'Autre' }}>Autre</option>
                            </select>
                        </div>

                        <!-- Carburant -->
                        <div class="col-md-6 mb-3">
                            <label for="carburant" class="form-label">
                                <i class="fas fa-gas-pump"></i> Carburant *
                            </label>
                            <select class="form-select" id="carburant" name="carburant" required>
                                <option value="">Sélectionner le carburant</option>
                                <option value="Essence" {{ 'selected' if vehicule.carburant == 'Essence' }}>Essence</option>
                                <option value="Diesel" {{ 'selected' if vehicule.carburant == 'Diesel' }}>Diesel</option>
                                <option value="Hybride" {{ 'selected' if vehicule.carburant == 'Hybride' }}>Hybride</option>
                                <option value="Électrique" {{ 'selected' if vehicule.carburant == 'Électrique' }}>Électrique</option>
                                <option value="GPL" {{ 'selected' if vehicule.carburant == 'GPL' }}>GPL</option>
                                <option value="GNV" {{ 'selected' if vehicule.carburant == 'GNV' }}>GNV</option>
                            </select>
                            <div class="invalid-feedback">
                                Veuillez sélectionner le type de carburant
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Kilométrage -->
                        <div class="col-md-6 mb-3">
                            <label for="kilometrage" class="form-label">
                                <i class="fas fa-tachometer-alt"></i> Kilométrage (km)
                            </label>
                            <input type="number" class="form-control" id="kilometrage" 
                                   name="kilometrage" min="0" 
                                   value="{{ vehicule.kilometrage }}">
                        </div>

                        <!-- Statut -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-info-circle"></i> Statut
                            </label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="disponible" {{ 'selected' if vehicule.statut == 'disponible' }}>Disponible</option>
                                <option value="affecte" {{ 'selected' if vehicule.statut == 'affecte' }}>Affecté</option>
                                <option value="en_maintenance" {{ 'selected' if vehicule.statut == 'en_maintenance' }}>En maintenance</option>
                                <option value="hors_service" {{ 'selected' if vehicule.statut == 'hors_service' }}>Hors service</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date d'acquisition -->
                        <div class="col-md-6 mb-3">
                            <label for="date_acquisition" class="form-label">
                                <i class="fas fa-calendar-plus"></i> Date d'acquisition
                            </label>
                            <input type="date" class="form-control" id="date_acquisition" 
                                   name="date_acquisition"
                                   value="{{ vehicule.date_acquisition or '' }}">
                        </div>

                        <!-- Prix d'acquisition -->
                        <div class="col-md-6 mb-3">
                            <label for="prix_acquisition" class="form-label">
                                <i class="fas fa-coins"></i> Prix d'acquisition (MAD)
                            </label>
                            <input type="number" class="form-control" id="prix_acquisition" 
                                   name="prix_acquisition" min="0" step="0.01"
                                   value="{{ vehicule.prix_acquisition or '' }}">
                        </div>
                    </div>

                    <!-- Informations de création -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Informations:</strong> 
                                Véhicule créé le {{ vehicule.date_creation }}
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('voir_vehicule', id=vehicule.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Formatage automatique de l'immatriculation
    const immatInput = document.getElementById('immatriculation');
    immatInput.addEventListener('input', function() {
        let value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        if (value.length > 2) {
            value = value.substring(0, 2) + '-' + value.substring(2);
        }
        if (value.length > 6) {
            value = value.substring(0, 6) + '-' + value.substring(6, 8);
        }
        this.value = value;
    });
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Gestion des Affectations - GesParc Auto{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-exchange-alt"></i> Gestion des Affectations</h1>
            <a href="{{ url_for('ajouter_affectation') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle affectation
            </a>
        </div>
    </div>
</div>

<!-- Tableau des affectations -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Liste des affectations
                <span class="badge bg-secondary ms-2">{{ affectations|length }} affectation(s)</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('export_affectations', format='csv') }}">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_affectations', format='xlsx') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLSX)
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('export_affectations', format='xls') }}">
                        <i class="fas fa-file-excel"></i> Excel (XLS)
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if affectations %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Véhicule</th>
                        <th>Conducteur</th>
                        <th>Date début</th>
                        <th>Date fin</th>
                        <th>Statut</th>
                        <th>Commentaire</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for affectation in affectations %}
                    <tr>
                        <td>
                            <strong>{{ affectation.immatriculation }}</strong><br>
                            <small class="text-muted">{{ affectation.marque }} {{ affectation.modele }}</small>
                        </td>
                        <td>{{ affectation.prenom }} {{ affectation.nom }}</td>
                        <td>{{ affectation.date_debut }}</td>
                        <td>{{ affectation.date_fin or '-' }}</td>
                        <td>
                            {% if affectation.statut == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% elif affectation.statut == 'terminee' %}
                                <span class="badge bg-secondary">Terminée</span>
                            {% elif affectation.statut == 'annulee' %}
                                <span class="badge bg-danger">Annulée</span>
                            {% endif %}
                        </td>
                        <td>{{ affectation.commentaire or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if affectation.statut == 'active' %}
                                <a href="{{ url_for('terminer_affectation', id=affectation.id) }}"
                                   class="btn btn-outline-warning btn-delete"
                                   data-item-name="cette affectation"
                                   title="Terminer l'affectation">
                                    <i class="fas fa-stop"></i>
                                </a>
                                {% endif %}
                                <button class="btn btn-outline-info btn-sm"
                                        title="Voir détails" disabled>
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune affectation enregistrée</h5>
            <p class="text-muted">Commencez par créer la première affectation véhicule-conducteur.</p>
            <a href="{{ url_for('ajouter_affectation') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle affectation
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

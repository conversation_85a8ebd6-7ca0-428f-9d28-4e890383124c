import sqlite3
from datetime import datetime, date
from typing import List, Dict, Optional

DATABASE = 'parc_automobile.db'

def get_db_connection():
    """Établit une connexion à la base de données SQLite"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

class Vehicule:
    def __init__(self, id=None, immatriculation=None, marque=None, modele=None, 
                 annee=None, couleur=None, kilometrage=0, carburant=None, 
                 statut='disponible', date_acquisition=None, prix_acquisition=None):
        self.id = id
        self.immatriculation = immatriculation
        self.marque = marque
        self.modele = modele
        self.annee = annee
        self.couleur = couleur
        self.kilometrage = kilometrage
        self.carburant = carburant
        self.statut = statut
        self.date_acquisition = date_acquisition
        self.prix_acquisition = prix_acquisition

    @staticmethod
    def get_all():
        """Récupère tous les véhicules"""
        conn = get_db_connection()
        vehicules = conn.execute('SELECT * FROM vehicules ORDER BY immatriculation').fetchall()
        conn.close()
        return vehicules

    @staticmethod
    def get_by_id(vehicule_id):
        """Récupère un véhicule par son ID"""
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE id = ?', (vehicule_id,)).fetchone()
        conn.close()
        return vehicule

    @staticmethod
    def get_by_immatriculation(immatriculation):
        """Récupère un véhicule par son immatriculation"""
        conn = get_db_connection()
        vehicule = conn.execute('SELECT * FROM vehicules WHERE immatriculation = ?', (immatriculation,)).fetchone()
        conn.close()
        return vehicule

    def save(self):
        """Sauvegarde le véhicule en base"""
        conn = get_db_connection()
        if self.id:
            # Mise à jour
            conn.execute('''
                UPDATE vehicules SET immatriculation=?, marque=?, modele=?, annee=?, 
                couleur=?, kilometrage=?, carburant=?, statut=?, date_acquisition=?, prix_acquisition=?
                WHERE id=?
            ''', (self.immatriculation, self.marque, self.modele, self.annee, self.couleur,
                  self.kilometrage, self.carburant, self.statut, self.date_acquisition, 
                  self.prix_acquisition, self.id))
        else:
            # Insertion
            cursor = conn.execute('''
                INSERT INTO vehicules (immatriculation, marque, modele, annee, couleur, 
                kilometrage, carburant, statut, date_acquisition, prix_acquisition)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.immatriculation, self.marque, self.modele, self.annee, self.couleur,
                  self.kilometrage, self.carburant, self.statut, self.date_acquisition, 
                  self.prix_acquisition))
            self.id = cursor.lastrowid
        conn.commit()
        conn.close()

    @staticmethod
    def delete(vehicule_id):
        """Supprime un véhicule"""
        conn = get_db_connection()
        conn.execute('DELETE FROM vehicules WHERE id = ?', (vehicule_id,))
        conn.commit()
        conn.close()

    @staticmethod
    def get_stats():
        """Récupère les statistiques des véhicules"""
        conn = get_db_connection()
        stats = {}
        
        # Total véhicules
        stats['total'] = conn.execute('SELECT COUNT(*) FROM vehicules').fetchone()[0]
        
        # Par statut
        statuts = conn.execute('''
            SELECT statut, COUNT(*) as count 
            FROM vehicules 
            GROUP BY statut
        ''').fetchall()
        
        for statut in statuts:
            stats[statut['statut']] = statut['count']
            
        conn.close()
        return stats

class Conducteur:
    def __init__(self, id=None, nom=None, prenom=None, numero_permis=None, 
                 date_permis=None, telephone=None, email=None, statut='actif'):
        self.id = id
        self.nom = nom
        self.prenom = prenom
        self.numero_permis = numero_permis
        self.date_permis = date_permis
        self.telephone = telephone
        self.email = email
        self.statut = statut

    @staticmethod
    def get_all():
        """Récupère tous les conducteurs"""
        conn = get_db_connection()
        conducteurs = conn.execute('SELECT * FROM conducteurs ORDER BY nom, prenom').fetchall()
        conn.close()
        return conducteurs

    @staticmethod
    def get_by_id(conducteur_id):
        """Récupère un conducteur par son ID"""
        conn = get_db_connection()
        conducteur = conn.execute('SELECT * FROM conducteurs WHERE id = ?', (conducteur_id,)).fetchone()
        conn.close()
        return conducteur

    def save(self):
        """Sauvegarde le conducteur en base"""
        conn = get_db_connection()
        if self.id:
            # Mise à jour
            conn.execute('''
                UPDATE conducteurs SET nom=?, prenom=?, numero_permis=?, date_permis=?, 
                telephone=?, email=?, statut=? WHERE id=?
            ''', (self.nom, self.prenom, self.numero_permis, self.date_permis,
                  self.telephone, self.email, self.statut, self.id))
        else:
            # Insertion
            cursor = conn.execute('''
                INSERT INTO conducteurs (nom, prenom, numero_permis, date_permis, 
                telephone, email, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (self.nom, self.prenom, self.numero_permis, self.date_permis,
                  self.telephone, self.email, self.statut))
            self.id = cursor.lastrowid
        conn.commit()
        conn.close()

    @staticmethod
    def delete(conducteur_id):
        """Supprime un conducteur"""
        conn = get_db_connection()
        conn.execute('DELETE FROM conducteurs WHERE id = ?', (conducteur_id,))
        conn.commit()
        conn.close()

class Maintenance:
    def __init__(self, id=None, vehicule_id=None, type_maintenance=None, description=None,
                 date_maintenance=None, cout=None, kilometrage_maintenance=None, 
                 garage=None, statut='planifiee'):
        self.id = id
        self.vehicule_id = vehicule_id
        self.type_maintenance = type_maintenance
        self.description = description
        self.date_maintenance = date_maintenance
        self.cout = cout
        self.kilometrage_maintenance = kilometrage_maintenance
        self.garage = garage
        self.statut = statut

    @staticmethod
    def get_all():
        """Récupère toutes les maintenances avec les infos véhicules"""
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT m.*, v.immatriculation, v.marque, v.modele 
            FROM maintenances m
            JOIN vehicules v ON m.vehicule_id = v.id
            ORDER BY m.date_maintenance DESC
        ''').fetchall()
        conn.close()
        return maintenances

    @staticmethod
    def get_by_vehicule(vehicule_id):
        """Récupère les maintenances d'un véhicule"""
        conn = get_db_connection()
        maintenances = conn.execute('''
            SELECT * FROM maintenances 
            WHERE vehicule_id = ? 
            ORDER BY date_maintenance DESC
        ''', (vehicule_id,)).fetchall()
        conn.close()
        return maintenances

    def save(self):
        """Sauvegarde la maintenance en base"""
        conn = get_db_connection()
        if self.id:
            # Mise à jour
            conn.execute('''
                UPDATE maintenances SET vehicule_id=?, type_maintenance=?, description=?, 
                date_maintenance=?, cout=?, kilometrage_maintenance=?, garage=?, statut=?
                WHERE id=?
            ''', (self.vehicule_id, self.type_maintenance, self.description, 
                  self.date_maintenance, self.cout, self.kilometrage_maintenance,
                  self.garage, self.statut, self.id))
        else:
            # Insertion
            cursor = conn.execute('''
                INSERT INTO maintenances (vehicule_id, type_maintenance, description, 
                date_maintenance, cout, kilometrage_maintenance, garage, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.vehicule_id, self.type_maintenance, self.description, 
                  self.date_maintenance, self.cout, self.kilometrage_maintenance,
                  self.garage, self.statut))
            self.id = cursor.lastrowid
        conn.commit()
        conn.close()

class Affectation:
    def __init__(self, id=None, vehicule_id=None, conducteur_id=None, date_debut=None,
                 date_fin=None, statut='active', commentaire=None):
        self.id = id
        self.vehicule_id = vehicule_id
        self.conducteur_id = conducteur_id
        self.date_debut = date_debut
        self.date_fin = date_fin
        self.statut = statut
        self.commentaire = commentaire

    @staticmethod
    def get_all():
        """Récupère toutes les affectations avec les infos véhicules et conducteurs"""
        conn = get_db_connection()
        affectations = conn.execute('''
            SELECT a.*, v.immatriculation, v.marque, v.modele,
                   c.nom, c.prenom
            FROM affectations a
            JOIN vehicules v ON a.vehicule_id = v.id
            JOIN conducteurs c ON a.conducteur_id = c.id
            ORDER BY a.date_debut DESC
        ''').fetchall()
        conn.close()
        return affectations

    def save(self):
        """Sauvegarde l'affectation en base"""
        conn = get_db_connection()
        if self.id:
            # Mise à jour
            conn.execute('''
                UPDATE affectations SET vehicule_id=?, conducteur_id=?, date_debut=?, 
                date_fin=?, statut=?, commentaire=? WHERE id=?
            ''', (self.vehicule_id, self.conducteur_id, self.date_debut, 
                  self.date_fin, self.statut, self.commentaire, self.id))
        else:
            # Insertion
            cursor = conn.execute('''
                INSERT INTO affectations (vehicule_id, conducteur_id, date_debut, 
                date_fin, statut, commentaire)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (self.vehicule_id, self.conducteur_id, self.date_debut, 
                  self.date_fin, self.statut, self.commentaire))
            self.id = cursor.lastrowid
        conn.commit()
        conn.close()
